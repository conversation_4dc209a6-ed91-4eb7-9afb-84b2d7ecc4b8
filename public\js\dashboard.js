// Dashboard View Switching, Chart Rendering, and Profile Updates

// Chart.js Variable
let agentStatusChartInstance = null;

// Background Dashboard Initialization
async function initializeDashboardInBackground() {
    console.log('Starting background dashboard initialization...');

    try {
        // Start multiple initialization tasks in parallel
        const initTasks = [];

        // Remove redundant profile loading - profile is already loaded during login
        // initTasks.push(loadUserProfile(false)); // REMOVED to prevent loading loops

        // Pre-initialize overview content preparation
        initTasks.push(prepareOverviewContent());

        // Pre-load any critical dashboard data
        initTasks.push(preloadDashboardData());

        // Wait for all background tasks to complete
        await Promise.allSettled(initTasks);

        console.log('Background dashboard initialization completed');

        // Small delay to ensure smooth transition timing
        await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
        console.error('Error during background dashboard initialization:', error);
        // Don't throw - let the dashboard load normally even if background init fails
    }
}

// Prepare overview content without showing it
async function prepareOverviewContent() {
    try {
        console.log('Preparing overview content in background...');

        // Get user role and company from current user
        const userRole = currentUser?.role || 'Client';
        const userCompany = currentUser?.company;

        if (!userCompany || userCompany === 'Pending') {
            return; // Skip if no company set up yet
        }

        // Pre-load data that will be needed for overview
        if (userRole === 'Super Admin') {
            // Pre-load companies for super admin
            await loadCompanies().catch(err => console.log('Background company load failed:', err));
        } else {
            // Pre-load agents for specific company
            await loadAIAgents(userCompany).catch(err => console.log('Background agents load failed:', err));
        }

        console.log('Overview content preparation completed');
    } catch (error) {
        console.error('Error preparing overview content:', error);
    }
}

// Pre-load critical dashboard data
async function preloadDashboardData() {
    try {
        console.log('Pre-loading dashboard data...');

        // Pre-load integration statuses if user will see integrations
        if (typeof loadIntegrationStatuses === 'function') {
            loadIntegrationStatuses().catch(err => console.log('Background integration load failed:', err));
        }

        // Pre-load help support status info - REMOVED

        // Pre-load notifications and update notification badge
        if (typeof updateNotificationBadge === 'function') {
            updateNotificationBadge().catch(err => console.log('Background notification badge update failed:', err));
        }

        // Pre-load notification statistics for the notifications page
        if (typeof loadNotificationStatistics === 'function') {
            loadNotificationStatistics().catch(err => console.log('Background notification statistics load failed:', err));
        }

        // Pre-cache notifications data for faster loading when user visits notifications page
        preloadNotificationsData().catch(err => console.log('Background notifications data preload failed:', err));

        console.log('Dashboard data pre-loading completed');
    } catch (error) {
        console.error('Error pre-loading dashboard data:', error);
    }
}

// Dashboard Internal View Switching
async function activateDashboardView(viewId) {
    const viewsContainer = document.getElementById('dashboard-views-container');
    if (!viewsContainer) {
         console.warn("Dashboard views container not found.");
        return;
    }

    // Check if we're leaving the Overview view and clean up real-time connections
    const currentActiveView = viewsContainer.querySelector(':not(.hidden)');
    if (currentActiveView && currentActiveView.id === 'dashboard-overview-view' && viewId !== 'dashboard-overview-view') {
        console.log('Leaving Overview view, cleaning up real-time connections...');
        if (typeof cleanupRealtimeConnections === 'function') {
            cleanupRealtimeConnections();
        }
    }

    // Update active state for navigation links
    const navLinks = document.querySelectorAll('#dashboard-sidebar-nav .nav-link');
    navLinks.forEach(link => {
        if (link.getAttribute('data-view-id') === viewId) {
            link.classList.add('bg-[#1A1A1A]', 'text-[#00BFFF]');
            link.classList.remove('bg-transparent');
        } else {
            link.classList.remove('bg-[#1A1A1A]', 'text-[#00BFFF]');
            link.classList.add('bg-transparent');
        }
    });

    // Hide all dashboard views
    Array.from(viewsContainer.children).forEach(view => {
        view.classList.add('hidden');
    });

    // Show the target view
    const targetView = document.getElementById(viewId);
    // NEW DEFAULT VIEW: Overview
    const defaultViewId = 'dashboard-overview-view';

    if (targetView) {
        targetView.classList.remove('hidden');

        // Handle specific view initialization
        if (viewId === 'dashboard-settings-view') {
            // Profile is already loaded during login - just populate form
            populateSettingsForm();

        } else if (viewId === 'dashboard-overview-view') {
            // Profile is already loaded during login - just initialize view
            await initializeOverviewView();

            // Only load Overview content if it hasn't been loaded yet
            if (!window.overviewContentLoaded) {
                // Load Overview content based on user role
                // Reduced timeout since background initialization may have pre-loaded data
                setTimeout(async () => {
                    console.log('Loading Overview content for the first time...');
                    if (typeof initializeOverviewContent === 'function') {
                        try {
                            await initializeOverviewContent();
                            window.overviewContentLoaded = true; // Mark as loaded
                            console.log('Overview content loaded successfully');
                        } catch (error) {
                            console.error('Error loading overview content:', error);
                        }
                    } else {
                        console.error('initializeOverviewContent function not found!');
                    }
                }, 50);
            } else {
                console.log('Overview content already loaded, skipping refresh');
            }
        } else if (viewId === 'dashboard-ai-agent-management-view') {
            // Load companies for the cards when AI Agent Management view is activated
            console.log('Loading companies for AI Agent Management view...');

            // Add a small delay to ensure all scripts are loaded
            setTimeout(async () => {
                if (typeof populateCompanyCards === 'function') {
                    console.log('populateCompanyCards function found, calling it...');
                    try {
                        await populateCompanyCards();
                        console.log('populateCompanyCards completed successfully');
                    } catch (error) {
                        console.error('Error in populateCompanyCards:', error);
                    }
                } else {
                    console.error('populateCompanyCards function not found!');
                    console.log('Available functions:', Object.getOwnPropertyNames(window).filter(name => name.includes('Company') || name.includes('company')));
                }

                // Also load existing AI agents
                if (typeof populateAIAgentsList === 'function') {
                    console.log('populateAIAgentsList function found, calling it...');
                    try {
                        await populateAIAgentsList();
                        console.log('populateAIAgentsList completed successfully');
                    } catch (error) {
                        console.error('Error in populateAIAgentsList:', error);
                    }
                } else {
                    console.error('populateAIAgentsList function not found!');
                }
            }, 100);

        } else if (viewId === 'dashboard-company-management-view') {
            // Load companies for the Company Management view
            console.log('Loading companies for Company Management view...');

            setTimeout(async () => {
                if (typeof populateCompaniesManagement === 'function') {
                    console.log('populateCompaniesManagement function found, calling it...');
                    try {
                        await populateCompaniesManagement();
                        console.log('populateCompaniesManagement completed successfully');
                    } catch (error) {
                        console.error('Error in populateCompaniesManagement:', error);
                    }
                } else {
                    console.error('populateCompaniesManagement function not found!');
                }
            }, 100);

        } else if (viewId === 'dashboard-client-management-view') {
            // Check if user has Owner role before allowing access to Client Management
            const userRole = window.cachedUserProfile?.role || currentUser?.role || 'Client';

            if (userRole !== 'Owner') {
                console.warn('Access denied: Client Management is only available to Owner users');
                showToast('Access denied: This feature is only available to Owner users', 'error');
                // Redirect to Overview instead
                activateDashboardView('dashboard-overview-view');
                return;
            }

            // Load clients for the Client Management view
            console.log('Loading clients for Client Management view...');

            setTimeout(async () => {
                if (typeof populateClientsManagement === 'function') {
                    console.log('populateClientsManagement function found, calling it...');
                    try {
                        await populateClientsManagement();
                        console.log('populateClientsManagement completed successfully');
                    } catch (error) {
                        console.error('Error in populateClientsManagement:', error);
                    }
                } else {
                    console.error('populateClientsManagement function not found!');
                }
            }, 100);

        } else if (viewId === 'dashboard-integrations-view') {
            // Initialize integrations page
            console.log('Loading Integrations view...');

            // Reduced timeout since background initialization may have pre-loaded data
            setTimeout(async () => {
                if (typeof initializeIntegrationsPage === 'function') {
                    console.log('initializeIntegrationsPage function found, calling it...');
                    try {
                        await initializeIntegrationsPage();
                        console.log('initializeIntegrationsPage completed successfully');
                    } catch (error) {
                        console.error('Error in initializeIntegrationsPage:', error);
                    }
                } else {
                    console.error('initializeIntegrationsPage function not found!');
                }
            }, 50);

        } else if (viewId === 'dashboard-notifications-view') {
            // Initialize notifications page
            console.log('Loading Notifications view...');

            // Reduced timeout since background initialization may have pre-loaded data
            setTimeout(async () => {
                if (typeof initializeNotificationsPage === 'function') {
                    console.log('initializeNotificationsPage function found, calling it...');
                    try {
                        await initializeNotificationsPage();
                        console.log('initializeNotificationsPage completed successfully');
                    } catch (error) {
                        console.error('Error in initializeNotificationsPage:', error);
                    }
                } else {
                    console.error('initializeNotificationsPage function not found!');
                }
            }, 50);

        } else if (viewId === 'dashboard-help-support-view') {
            // Initialize help and support page
            console.log('Loading Help & Support view...');

            // Modify content based on user role
            await initializeHelpSupportPage();

            // Load integration status for help page
            if (typeof loadIntegrationStatusForHelp === 'function') {
                await loadIntegrationStatusForHelp();
            }

        } else {
            console.log(`Activated Dashboard View: ${viewId}`);
        }

    } else {
         // If the requested view ID is not found (e.g., one of the removed ones),
         // fall back to the new default view.
         console.warn(`Dashboard view with ID "${viewId}" not found or removed. Activating default view: "${defaultViewId}"`);
         const defaultView = document.getElementById(defaultViewId);
         if (defaultView) {
             defaultView.classList.remove('hidden');
             viewId = defaultViewId; // Update viewId for highlighting
             initializeOverviewView(); // Initialize the default overview view
         } else {
             console.error(`Default dashboard view "${defaultViewId}" not found. Cannot activate any view.`);

             return; // Cannot activate any view
         }
    }

    // Update active navigation link in the sidebar
    const dashboardNavLinks = document.querySelectorAll('#ai-dashboard-page nav a');
    dashboardNavLinks.forEach(link => {
        link.classList.remove('bg-[#1A1A1A]', 'font-semibold');
        link.classList.add('bg-transparent');
    });
    const activeLink = document.querySelector(`#ai-dashboard-page nav a[data-view-id="${viewId}"]`);
    if (activeLink) {
        activeLink.classList.add('bg-[#1A1A1A]', 'font-semibold');
        activeLink.classList.remove('bg-transparent');
    } else {
         console.warn(`Active navigation link for view ID "${viewId}" not found in sidebar.`);
    }
}

// Function to initialize/update the Overview View content
async function initializeOverviewView() {
    console.log("Initializing Dashboard Overview View...");

    // Initialize real-time updates
    if (typeof initializeRealtimeUpdates === 'function') {
        // Clean up any existing real-time updates
        if (window.realtimeUpdateInterval) {
            clearInterval(window.realtimeUpdateInterval);
            window.realtimeUpdateInterval = null;
        }
        if (window.websocketConnection) {
            window.websocketConnection.close();
            window.websocketConnection = null;
        }

        // Clean up existing statistics subscriptions
        if (typeof cleanupStatisticsSubscriptions === 'function') {
            cleanupStatisticsSubscriptions();
        }

        // Start new real-time updates
        await initializeRealtimeUpdates();
    } else {
        console.warn('Real-time updates function not available');
    }

    // Setup refresh button for overview agents
    const refreshBtn = document.getElementById('refresh-overview-agents-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            console.log('Refresh overview agents button clicked');
            // Reset the overview content loaded flag to force a refresh
            window.overviewContentLoaded = false;
            if (typeof refreshOverviewAgents === 'function') {
                await refreshOverviewAgents();
            }
            // Reload the overview content
            if (typeof initializeOverviewContent === 'function') {
                await initializeOverviewContent();
                window.overviewContentLoaded = true;
            }
        });
    }
}

// Populate settings form with user data
function populateSettingsForm() {
    if (!currentUser) {
        console.warn('No current user data available for settings form');
        return;
    }

    // Populate form fields if they exist
    const formFields = {
        'settings-full-name': currentUser.fullName,
        'settings-email': currentUser.email,
        'settings-company': currentUser.company
    };

    Object.entries(formFields).forEach(([fieldId, value]) => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.value = value || '';
        }
    });
}

// Handle settings form submission
async function handleSettingsUpdate(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const fullName = formData.get('full_name');
    const email = formData.get('email');
    const company = formData.get('company');
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    // Clear any existing errors
    clearFormErrors(form);
    
    // Validate form
    const errors = validateForm({ 
        full_name: fullName, 
        email, 
        company 
    }, {
        full_name: { required: true, label: 'Full Name' },
        email: { required: true, type: 'email', label: 'Email' },
        company: { required: true, label: 'Company Name' }
    });
    
    // Validate password if provided
    if (newPassword) {
        if (!validatePassword(newPassword)) {
            errors.push('Password must be at least 8 characters with uppercase, lowercase, and number');
        }
        if (newPassword !== confirmPassword) {
            errors.push('Passwords do not match');
        }
    }
    
    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }
    
    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);
    
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Authentication service unavailable');
        }
        
        // Update user metadata
        const { error: updateError } = await supabase.auth.updateUser({
            data: {
                full_name: fullName,
                company: company
            }
        });
        
        if (updateError) {
            throw updateError;
        }
        
        // Update password if provided
        if (newPassword) {
            const { error: passwordError } = await supabase.auth.updateUser({
                password: newPassword
            });
            
            if (passwordError) {
                throw passwordError;
            }
        }
        
        // Update profile in database
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
            const { error: profileError } = await supabase
                .from('user_profiles')
                .upsert({
                    user_id: user.id,
                    full_name: fullName,
                    company_name: company,
                    updated_at: new Date().toISOString()
                });

            if (profileError) {
                console.warn('Profile update error:', profileError);
                // Don't throw here as the main update succeeded
            }
        }
        
        // Update current user object
        currentUser.fullName = fullName;
        currentUser.email = email;
        currentUser.company = company;

        // Create profile object for UI update
        const userProfile = {
            fullName: fullName,
            email: email,
            company: company,
            role: currentUser.role
        };

        // Update UI
        updateProfileUI(userProfile);
        updateNavForLoginState();
        
        showToast('Profile updated successfully!', 'success');
        
        // Clear password fields
        const passwordFields = form.querySelectorAll('input[type="password"]');
        passwordFields.forEach(field => field.value = '');
        
    } catch (error) {
        console.error('Settings update error:', error);
        let errorMessage = 'Failed to update profile. Please try again.';
        
        if (error.message.includes('email')) {
            errorMessage = 'Email update failed. Please check your email format.';
        } else if (error.message.includes('password')) {
            errorMessage = 'Password update failed. Please check your password requirements.';
        }
        
        showFormError(form, errorMessage);
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Handle contact form submission
async function handleContactForm(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const name = formData.get('name');
    const company = formData.get('company');
    const email = formData.get('email');
    const message = formData.get('message');
    
    // Clear any existing errors
    clearFormErrors(form);
    
    // Validate form
    const errors = validateForm({ name, company, email, message }, {
        name: { required: true, label: 'Name' },
        company: { required: true, label: 'Company' },
        email: { required: true, type: 'email', label: 'Email' },
        message: { required: true, label: 'Message' }
    });
    
    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }
    
    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);
    
    try {
        // Here you would typically send the form data to your backend
        // For now, we'll simulate a successful submission
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        showToast('Thank you for your message! We\'ll get back to you soon.', 'success');
        form.reset();
        
    } catch (error) {
        console.error('Contact form error:', error);
        showFormError(form, 'Failed to send message. Please try again.');
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Handle AI agent form submission
async function handleAIAgentForm(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const agentName = formData.get('agent-name');
    const company = formData.get('company');

    // Clear any existing errors
    clearFormErrors(form);

    // Debug logging
    console.log('Form submission - Agent Name:', agentName);
    console.log('Form submission - Company:', company);
    console.log('Hidden input value:', document.getElementById('selected-company')?.value);

    // Get company value directly from hidden input if FormData doesn't have it
    const companyValue = company || document.getElementById('selected-company')?.value;

    // Validate form
    const errors = validateForm({ 'agent-name': agentName, company: companyValue }, {
        'agent-name': { required: true, label: 'Agent Name' },
        company: { required: true, label: 'Company' }
    });

    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }
    
    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);
    
    try {
        // Get current user
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        console.log('Creating AI Agent:', { agentName, company: companyValue, userId: user.id });

        // Create AI agent using API function
        const agentData = await createAIAgent({
            name: agentName,
            company_name: companyValue,
            status: 'active',
            description: `AI Agent for ${companyValue}`
        });

        console.log('AI Agent created successfully:', agentData);

        showToast(`AI Agent "${agentName}" created successfully for ${companyValue}!`, 'success');

        // Log agent creation activity
        await logAgentActivity('created', 'AI Agent Created', `Created new AI agent "${agentName}" for ${companyValue}`, {
            agent_name: agentName,
            company_name: companyValue,
            agent_id: agentData.id
        });
        form.reset();

        // Reset company selection
        const hiddenInput = form.querySelector('#company');
        if (hiddenInput) {
            hiddenInput.value = '';
        }

        // Clear visual selection from company cards
        const allCards = document.querySelectorAll('.company-card');
        allCards.forEach(card => {
            card.classList.remove('border-[#00BFFF]', 'bg-[#00BFFF]/10');
            card.classList.add('border-[#333]');
            const checkIcon = card.querySelector('.company-card-check');
            if (checkIcon) {
                checkIcon.classList.add('hidden');
            }
        });

        // Refresh the AI agents list to show the new agent
        if (typeof populateAIAgentsList === 'function') {
            console.log('Refreshing AI agents list after creation...');
            await populateAIAgentsList();
            console.log('AI agents list refresh completed');
        } else {
            console.error('populateAIAgentsList function not available for refresh');
        }

    } catch (error) {
        console.error('AI Agent creation error:', error);
        showFormError(form, 'Failed to create AI agent. Please try again.');
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Overview-specific functions for company selection and agent display

// Function to initialize Overview content based on user role
async function initializeOverviewContent() {
    console.log('initializeOverviewContent called');

    // Get user role and company from current user
    const userRole = currentUser?.role || 'Client';
    const userCompany = currentUser?.company;

    console.log('User role:', userRole, 'User company:', userCompany);

    const companySelectionDiv = document.getElementById('overview-company-selection');
    const agentsContainer = document.getElementById('overview-agents-container');
    const statisticsSection = document.getElementById('overview-statistics-section');

    // Find Recent Activity section by looking for the h3 with "Recent Activity" text
    let recentActivitySection = null;
    const h3Elements = document.querySelectorAll('#dashboard-overview-view h3');
    for (const h3 of h3Elements) {
        if (h3.textContent.includes('Recent Activity')) {
            // Walk up the DOM tree to find the container div
            let parent = h3.parentElement;
            while (parent && !parent.classList.contains('mb-8')) {
                parent = parent.parentElement;
            }
            recentActivitySection = parent || h3.parentElement;
            break;
        }
    }

    if (!agentsContainer) {
        console.error('Overview agents container not found');
        return;
    }

    if (userRole === 'Client') {
        // For Client role: Hide company selection, statistics section, and automatically load their company's agents
        console.log('Client role detected - hiding company selection, statistics, and loading user company agents');

        if (companySelectionDiv) {
            companySelectionDiv.style.display = 'none';
        }

        // Hide statistics section for Client role
        if (statisticsSection) {
            statisticsSection.style.display = 'none';
        }

        // Hide Recent Activity section for Client role
        if (recentActivitySection) {
            recentActivitySection.style.display = 'none';
            console.log('Recent Activity section hidden for Client role');
        } else {
            console.warn('Recent Activity section not found for hiding');
        }

        if (userCompany && userCompany !== 'Not specified') {
            // Automatically load agents for the user's company
            await loadOverviewAgents(userCompany);
        } else {
            // Show message that user needs to set their company
            agentsContainer.innerHTML = `
                <div class="flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg">
                    <div class="text-center">
                        <i class="fas fa-building text-4xl text-gray-500 mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-300 mb-2">Company Not Set</h4>
                        <p class="text-sm text-gray-400 mb-4">Please update your profile with your company information</p>
                        <a href="#" class="inline-flex items-center px-4 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-white rounded-md text-sm transition-colors" data-view-id="dashboard-settings-view">
                            <i class="fas fa-cog mr-2"></i>Go to Settings
                        </a>
                    </div>
                </div>
            `;

            // Add click handler for the settings link
            const settingsLink = agentsContainer.querySelector('a[data-view-id="dashboard-settings-view"]');
            if (settingsLink) {
                settingsLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    activateDashboardView('dashboard-settings-view');
                });
            }
        }
    } else {
        // For Owner role: Show company selection interface and statistics section, hide Recent Activity
        console.log('Owner role detected - showing company selection interface and statistics, hiding Recent Activity');

        if (companySelectionDiv) {
            companySelectionDiv.style.display = 'block';
        }

        // Show statistics section for Owner role
        if (statisticsSection) {
            statisticsSection.style.display = 'grid';
        }

        // Hide Recent Activity section for Owner role
        if (recentActivitySection) {
            recentActivitySection.style.display = 'none';
            console.log('Recent Activity section hidden for Owner role');
        } else {
            console.warn('Recent Activity section not found for hiding');
        }

        // Load companies for selection
        await populateOverviewCompanyCards();
    }
}

// Function to populate company cards for Overview section (Owner role only)
async function populateOverviewCompanyCards() {
    console.log('populateOverviewCompanyCards called');

    const container = document.getElementById('overview-company-cards-container');
    const loadingElement = document.getElementById('overview-companies-loading');

    if (!container) {
        console.warn('Overview company cards container not found');
        return;
    }

    console.log('Overview company cards container found, loading companies...');

    try {
        const companies = await loadCompanies();
        console.log('Companies loaded for overview cards:', companies);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Clear existing cards (except loading element)
        const existingCards = container.querySelectorAll('.company-card');
        existingCards.forEach(card => card.remove());

        if (companies.length === 0) {
            // Show empty state
            const emptyState = document.createElement('div');
            emptyState.className = 'flex items-center justify-center p-6 bg-[#1A1A1A] border border-[#333] rounded-lg';
            emptyState.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-building text-4xl text-gray-500 mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-300 mb-2">No Companies Found</h4>
                    <p class="text-sm text-gray-400">Create a company in the Company Management section first</p>
                </div>
            `;
            container.appendChild(emptyState);
            return;
        }

        // Create company cards
        companies.forEach((company, index) => {
            console.log(`Creating overview card for company ${index + 1}:`, company);

            const card = document.createElement('div');
            card.className = 'company-card cursor-pointer p-4 bg-[#1A1A1A] border border-[#333] rounded-lg hover:border-[#00BFFF]/50 transition-all duration-200';

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                            <i class="fas fa-building text-[#00BFFF]"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-medium">${company.name}</h3>
                            <p class="text-sm text-gray-400">${company.user_count} user${company.user_count !== 1 ? 's' : ''}</p>
                        </div>
                    </div>
                    <div class="company-card-check hidden">
                        <i class="fas fa-check-circle text-[#00BFFF] text-xl"></i>
                    </div>
                </div>
            `;

            // Add click handler
            card.addEventListener('click', () => {
                console.log('Overview company card clicked:', company.name);
                selectOverviewCompanyCard(company.name, card);
            });

            container.appendChild(card);
        });

        console.log(`Created ${companies.length} overview company cards`);

    } catch (error) {
        console.error('Error loading overview companies:', error);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Show error state
        const errorState = document.createElement('div');
        errorState.className = 'flex items-center justify-center p-6 bg-[#1A1A1A] border border-red-500/50 rounded-lg';
        errorState.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                <h4 class="text-lg font-medium text-red-300 mb-2">Error Loading Companies</h4>
                <p class="text-sm text-gray-400 mb-4">${error.message}</p>
                <button onclick="populateOverviewCompanyCards()" class="px-4 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-white rounded-md text-sm transition-colors">
                    <i class="fas fa-retry mr-2"></i>Retry
                </button>
            </div>
        `;
        container.appendChild(errorState);
    }
}

// Function to select a company card in Overview section
function selectOverviewCompanyCard(companyName, cardElement) {
    console.log('Overview company selected:', companyName);

    // Update hidden input
    const hiddenInput = document.getElementById('overview-company');
    if (hiddenInput) {
        hiddenInput.value = companyName;
        console.log('Overview hidden input value set to:', hiddenInput.value);
    } else {
        console.error('Overview hidden company input not found!');
    }

    // Update visual selection
    const allCards = document.querySelectorAll('#overview-company-cards-container .company-card');
    allCards.forEach(card => {
        card.classList.remove('border-[#00BFFF]', 'bg-[#00BFFF]/10');
        card.classList.add('border-[#333]');
        const checkIcon = card.querySelector('.company-card-check');
        if (checkIcon) {
            checkIcon.classList.add('hidden');
        }
    });

    // Highlight selected card
    cardElement.classList.remove('border-[#333]');
    cardElement.classList.add('border-[#00BFFF]', 'bg-[#00BFFF]/10');
    const checkIcon = cardElement.querySelector('.company-card-check');
    if (checkIcon) {
        checkIcon.classList.remove('hidden');
    }

    // Show success feedback
    if (typeof showToast === 'function') {
        showToast(`Selected: ${companyName}`, 'success');
    }

    // Load agents for the selected company with a small delay to ensure smooth transition
    setTimeout(() => {
        loadOverviewAgents(companyName);
    }, 150);
}

// Function to load and display agents for the selected company in Overview
async function loadOverviewAgents(companyName) {
    console.log('loadOverviewAgents called with company:', companyName);

    const container = document.getElementById('overview-agents-container');
    if (!container) {
        console.error('Overview agents container not found');
        return;
    }

    // Update section title based on user role
    const userRole = currentUser?.role || 'Client';
    const sectionTitle = document.querySelector('#dashboard-overview-view h3');
    if (sectionTitle) {
        if (userRole === 'Client') {
            sectionTitle.textContent = 'Your AI Agents';
        } else {
            sectionTitle.textContent = 'Company Agents';
        }
    }

    // Add loading class to prevent bouncing
    container.classList.add('overview-content-loading');

    // Show loading state with fixed height to prevent layout shift
    container.innerHTML = `
        <div class="flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg" style="min-height: 200px;">
            <div class="flex items-center space-x-3">
                <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                <span class="text-gray-300">Loading agents for ${companyName}...</span>
            </div>
        </div>
    `;

    try {
        const agents = await loadAIAgents(companyName);
        console.log('Overview agents loaded:', agents);

        // Remove loading class and clear container smoothly
        container.classList.remove('overview-content-loading');
        container.classList.add('overview-content-loaded');

        // Use setTimeout to ensure smooth transition
        setTimeout(() => {
            container.innerHTML = '';

            if (agents.length === 0) {
            // Show empty state with role-appropriate messaging
            const userRole = window.cachedUserProfile?.role || currentUser?.role || 'Client';
            let emptyStateContent;

            if (userRole === 'Client') {
                emptyStateContent = `
                    <div class="text-center">
                        <i class="fas fa-robot text-4xl text-gray-500 mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-300 mb-2">No Agents Found</h4>
                        <p class="text-sm text-gray-400 mb-4">No AI agents have been created for your company yet</p>
                        <p class="text-xs text-gray-500">Contact your administrator to create AI agents for your company</p>
                    </div>
                `;
            } else {
                emptyStateContent = `
                    <div class="text-center">
                        <i class="fas fa-robot text-4xl text-gray-500 mb-4"></i>
                        <h4 class="text-lg font-medium text-gray-300 mb-2">No Agents Found</h4>
                        <p class="text-sm text-gray-400 mb-4">No AI agents have been created for ${companyName} yet</p>
                        <a href="#" class="inline-flex items-center px-4 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-white rounded-md text-sm transition-colors" data-view-id="dashboard-ai-agent-management-view">
                            <i class="fas fa-plus mr-2"></i>Create Agent
                        </a>
                    </div>
                `;
            }

                container.innerHTML = `
                    <div class="flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg" style="min-height: 200px;">
                        ${emptyStateContent}
                    </div>
                `;

                // Add click handler for the create agent link (Owner role only)
                if (userRole === 'Owner') {
                    const createAgentLink = container.querySelector('a[data-view-id="dashboard-ai-agent-management-view"]');
                    if (createAgentLink) {
                        createAgentLink.addEventListener('click', (e) => {
                            e.preventDefault();
                            activateDashboardView('dashboard-ai-agent-management-view');
                        });
                    }
                }
                return;
            }

            // Create agents list
            const agentsList = document.createElement('div');
            agentsList.className = 'space-y-3';

            agents.forEach((agent, index) => {
                console.log(`Creating overview agent card ${index + 1}:`, agent);

                const agentCard = document.createElement('div');
                agentCard.className = 'agent-card p-4 bg-[#1A1A1A] border border-[#333] rounded-lg hover:border-[#00BFFF]/50 transition-all duration-200';

                const createdDate = new Date(agent.created_at).toLocaleDateString();
                const statusColor = agent.status === 'active' ? 'text-green-400' : 'text-gray-400';
                const statusIcon = agent.status === 'active' ? 'fa-check-circle' : 'fa-pause-circle';

                agentCard.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                                <i class="fas fa-robot text-[#00BFFF] text-lg"></i>
                            </div>
                            <div>
                                <h4 class="text-white font-medium text-lg">${agent.name}</h4>
                                <p class="text-sm text-gray-400">${agent.company_name}</p>
                                <p class="text-xs text-gray-500">Created ${createdDate}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="text-right">
                                <div class="flex items-center space-x-2">
                                    <i class="fas ${statusIcon} ${statusColor}"></i>
                                    <span class="text-sm ${statusColor} capitalize">${agent.status}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                agentsList.appendChild(agentCard);
            });

            container.appendChild(agentsList);

            console.log(`Created ${agents.length} overview agent cards`);
        }, 100); // Small delay for smooth transition

    } catch (error) {
        console.error('Error loading overview agents:', error);

        // Remove loading class and show error state
        container.classList.remove('overview-content-loading');
        container.classList.add('overview-content-loaded');

        // Show error state with fixed height
        container.innerHTML = `
            <div class="flex items-center justify-center p-8 bg-[#1A1A1A] border border-red-500/50 rounded-lg" style="min-height: 200px;">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                    <h4 class="text-lg font-medium text-red-300 mb-2">Error Loading Agents</h4>
                    <p class="text-sm text-gray-400 mb-4">${error.message}</p>
                    <button onclick="loadOverviewAgents('${companyName}')" class="px-4 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-white rounded-md text-sm transition-colors">
                        <i class="fas fa-retry mr-2"></i>Retry
                    </button>
                </div>
            </div>
        `;
    }
}

// Function to refresh overview agents
async function refreshOverviewAgents() {
    const userRole = currentUser?.role || 'Client';
    const userCompany = currentUser?.company;

    if (userRole === 'Client') {
        // For Client role: Refresh their own company's agents
        if (userCompany && userCompany !== 'Not specified') {
            console.log('Refreshing overview agents for client company:', userCompany);
            await loadOverviewAgents(userCompany);
        } else {
            showToast('Company information not available', 'error');
        }
    } else {
        // For Owner role: Refresh selected company's agents
        const hiddenInput = document.getElementById('overview-company');
        const selectedCompany = hiddenInput?.value;

        if (selectedCompany) {
            console.log('Refreshing overview agents for selected company:', selectedCompany);
            await loadOverviewAgents(selectedCompany);
        } else {
            console.log('No company selected for overview refresh');
            showToast('Please select a company first', 'info');
        }
    }
}

// Company Management Functions

// Function to populate companies in the management view
async function populateCompaniesManagement() {
    console.log('populateCompaniesManagement called');

    const container = document.getElementById('companies-management-container');
    const loadingElement = document.getElementById('companies-management-loading');

    if (!container) {
        console.warn('Companies management container not found');
        return;
    }

    console.log('Companies management container found, loading companies...');

    // Clear search when refreshing
    clearCompanySearch();

    try {
        const companies = await loadCompanies();
        console.log('Companies loaded for management:', companies);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Clear any existing cards (except loading)
        const existingCards = container.querySelectorAll('.company-management-card');
        existingCards.forEach(card => card.remove());

        if (companies.length === 0) {
            // Show empty state
            const emptyState = document.createElement('div');
            emptyState.className = 'flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg col-span-full';
            emptyState.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-building text-4xl text-gray-500 mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-300 mb-2">No Companies Found</h4>
                    <p class="text-sm text-gray-400">Create your first company using the form above</p>
                </div>
            `;
            container.appendChild(emptyState);
            return;
        }

        // Create company management cards
        companies.forEach((company, index) => {
            console.log(`Creating management card for company ${index + 1}:`, company);

            const card = document.createElement('div');
            card.className = 'company-management-card p-4 md:p-5 bg-[#1A1A1A] border border-[#333] rounded-lg hover:border-[#00BFFF]/50 transition-all duration-200 cursor-pointer';
            card.dataset.companyId = company.id;
            card.dataset.companyName = company.name;

            const createdDate = new Date(company.created_at || Date.now()).toLocaleDateString();
            const userCount = company.user_count || 0;

            card.innerHTML = `
                <!-- Desktop Layout -->
                <div class="hidden md:flex items-center justify-between">
                    <div class="flex items-center space-x-4 flex-1 min-w-0">
                        <div class="w-12 h-12 rounded-full bg-[#00BFFF]/10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-building text-[#00BFFF] text-lg"></i>
                        </div>
                        <div class="min-w-0 flex-1">
                            <h4 class="text-white font-medium text-lg truncate">${company.name}</h4>
                            <p class="text-sm text-gray-400">${company.industry || 'Not specified'}</p>
                            <p class="text-xs text-gray-500">Users: ${userCount} • Created: ${createdDate}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 flex-shrink-0">
                        <div class="company-actions flex space-x-2">
                            <button class="company-edit-btn px-3 py-1 bg-[#333] hover:bg-[#444] text-white rounded-lg text-sm transition-all duration-200 min-h-[32px] flex items-center justify-center"
                                    data-company-id="${company.id}" title="Edit company">
                                <i class="fas fa-edit text-sm"></i>
                                <span class="ml-1">Edit</span>
                            </button>
                            <button class="company-view-btn px-3 py-1 bg-[#00BFFF]/10 hover:bg-[#00BFFF]/20 border border-[#00BFFF]/30 hover:border-[#00BFFF]/50 text-[#00BFFF] hover:text-white rounded-lg text-sm transition-all duration-200 min-h-[32px] flex items-center justify-center"
                                    data-company-id="${company.id}" title="View details">
                                <i class="fas fa-eye text-sm"></i>
                                <span class="ml-1">View</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Layout -->
                <div class="md:hidden">
                    <div class="flex items-start space-x-3 mb-4">
                        <div class="w-12 h-12 rounded-full bg-[#00BFFF]/10 flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-building text-[#00BFFF] text-lg"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="text-white font-medium text-lg truncate mb-1">${company.name}</h4>
                            <p class="text-sm text-gray-400 mb-1">${company.industry || 'Not specified'}</p>
                            <p class="text-xs text-gray-500">${userCount} user${userCount !== 1 ? 's' : ''} • Created ${createdDate}</p>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button class="company-edit-btn flex-1 px-4 py-2.5 bg-[#333] hover:bg-[#444] text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center min-h-[44px]"
                                data-company-id="${company.id}" title="Edit company">
                            <i class="fas fa-edit text-sm mr-2"></i>
                            <span>Edit</span>
                        </button>
                        <button class="company-view-btn flex-1 px-4 py-2.5 bg-[#00BFFF]/10 hover:bg-[#00BFFF]/20 border border-[#00BFFF]/30 hover:border-[#00BFFF]/50 text-[#00BFFF] hover:text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center min-h-[44px]"
                                data-company-id="${company.id}" title="View details">
                            <i class="fas fa-eye text-sm mr-2"></i>
                            <span>View</span>
                        </button>
                    </div>
                </div>
            `;

            // Add event listeners for action buttons
            const editBtn = card.querySelector('.company-edit-btn');
            const viewBtn = card.querySelector('.company-view-btn');

            if (editBtn) {
                editBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('Edit company clicked:', company.id);
                    // TODO: Implement edit functionality
                    showToast('Edit functionality coming soon!', 'info');
                });
            }

            if (viewBtn) {
                viewBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log('View company clicked:', company.id);
                    selectCompany(card, company);
                });
            }

            // Add click handler for selection (clicking anywhere on the card)
            card.addEventListener('click', () => {
                selectCompany(card, company);
            });

            container.appendChild(card);
        });

        console.log('Company management cards created successfully!');

        // Reset the details panel to empty state
        resetCompanyDetailsPanel();

        // Setup search functionality
        setupCompanySearch();

    } catch (error) {
        console.error('Error populating company management:', error);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Show error state
        const errorState = document.createElement('div');
        errorState.className = 'flex items-center justify-center p-8 bg-red-900/20 border border-red-700 rounded-lg';
        errorState.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-2"></i>
                <p class="text-red-300 mb-2">Error loading companies</p>
                <button onclick="populateCompaniesManagement()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Retry
                </button>
            </div>
        `;
        container.appendChild(errorState);

        // Reset the details panel to empty state
        resetCompanyDetailsPanel();
    }
}

// Function to select a company and show details in right panel
async function selectCompany(cardElement, company) {
    console.log('Selecting company:', company.name);

    // Add selecting animation to clicked card
    cardElement.classList.add('selecting');

    // Remove selection from all cards
    const allCards = document.querySelectorAll('.company-management-card');
    allCards.forEach(card => {
        card.classList.remove('selected');
        if (card !== cardElement) {
            card.classList.remove('selecting');
        }
    });

    // Show company details in right panel with animation
    await showCompanyDetailsInPanel(company);

    // Complete the selection animation
    setTimeout(() => {
        cardElement.classList.remove('selecting');
        cardElement.classList.add('selected');
    }, 200);
}

// Function to show company details in the right panel
async function showCompanyDetailsInPanel(company) {
    console.log('Showing company details in panel for:', company.name);

    const emptyState = document.getElementById('company-details-empty');
    const contentElement = document.getElementById('company-details-content');

    // Start exit animation if content is already visible
    if (!contentElement.classList.contains('hidden')) {
        contentElement.classList.add('company-details-exiting');
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Hide empty state and show content
    emptyState.classList.add('hidden');
    contentElement.classList.remove('hidden');
    contentElement.classList.remove('company-details-exiting');
    contentElement.classList.add('company-details-entering');

    // Show loading state with animation
    contentElement.innerHTML = `
        <div class="company-details-loading flex items-center justify-center py-8">
            <div class="flex items-center space-x-3">
                <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                <span class="text-gray-300">Loading company details...</span>
            </div>
        </div>
    `;

    try {
        // Add a small delay to make the loading state visible
        await new Promise(resolve => setTimeout(resolve, 300));

        // Load company users
        const users = await loadCompanyUsers(company.name);
        console.log('Company users loaded:', users);

        // Build the details content
        const detailsHTML = buildCompanyDetailsHTML(company, users);

        // Animate content in
        contentElement.innerHTML = detailsHTML;

        // Trigger enter animation
        setTimeout(() => {
            contentElement.classList.remove('company-details-entering');
            contentElement.classList.add('company-details-entered');
        }, 50);

        // Clean up animation classes after animation completes
        setTimeout(() => {
            contentElement.classList.remove('company-details-entered');
        }, 450);

    } catch (error) {
        console.error('Error loading company details:', error);

        // Show error state with animation
        contentElement.innerHTML = `
            <div class="text-center py-8 bg-red-900/20 rounded-lg border border-red-700">
                <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                <p class="text-red-300 mb-2">Error loading company details</p>
                <button onclick="showCompanyDetailsInPanel(${JSON.stringify(company).replace(/"/g, '&quot;')})"
                        class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors">
                    <i class="fas fa-retry mr-1"></i>Retry
                </button>
            </div>
        `;

        // Trigger enter animation for error state
        setTimeout(() => {
            contentElement.classList.remove('company-details-entering');
            contentElement.classList.add('company-details-entered');
        }, 50);

        setTimeout(() => {
            contentElement.classList.remove('company-details-entered');
        }, 450);
    }
}

// Function to toggle company details expansion (legacy - kept for compatibility)
async function toggleCompanyDetails(cardElement, company) {
    // For the new layout, just select the company
    await selectCompany(cardElement, company);
}

// Function to build company details HTML
function buildCompanyDetailsHTML(company, users) {
    const createdDate = new Date(company.created_at || Date.now()).toLocaleDateString();
    const userCount = users.length;

    let detailsHTML = `
        <div class="space-y-6">
            <!-- Company Header -->
            <div class="flex items-center space-x-4 pb-4 border-b border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-[#00BFFF] to-[#0080FF] rounded-full flex items-center justify-center">
                    <i class="fas fa-building text-white text-xl"></i>
                </div>
                <div>
                    <h4 class="text-xl font-semibold text-white">${company.name}</h4>
                    <p class="text-gray-400">${company.industry || 'Not specified'}</p>
                    <p class="text-sm text-gray-500">Created: ${createdDate}</p>
                </div>
            </div>

            <!-- Company Information -->
            <div>
                <h5 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-info-circle text-[#00BFFF] mr-2"></i>
                    Company Information
                </h5>
                <div class="grid grid-cols-1 gap-3 text-sm">
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Company Name:</span>
                        <p class="text-white font-medium mt-1">${company.name}</p>
                    </div>
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Industry:</span>
                        <p class="text-white font-medium mt-1">${company.industry || 'Not specified'}</p>
                    </div>
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Total Users:</span>
                        <p class="text-white font-medium mt-1">${userCount}</p>
                    </div>
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Company ID:</span>
                        <p class="text-gray-300 font-mono text-xs mt-1 break-all">${company.id}</p>
                    </div>
                    ${company.description ? `
                        <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                            <span class="text-gray-400">Description:</span>
                            <p class="text-white font-medium mt-1">${company.description}</p>
                        </div>
                    ` : ''}
                </div>
            </div>

            <!-- Connected Users Section -->
            <div>
                <h5 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-users text-[#00BFFF] mr-2"></i>
                    Connected Users (${userCount})
                </h5>
    `;

    if (userCount === 0) {
        detailsHTML += `
                <div class="text-center py-6 bg-[#1A1A1A] rounded-lg border border-gray-800">
                    <i class="fas fa-user-slash text-gray-500 text-2xl mb-2"></i>
                    <p class="text-gray-400 text-sm">No users connected to this company</p>
                </div>
        `;
    } else {
        detailsHTML += `<div class="space-y-2">`;

        users.forEach(user => {
            const joinedDate = new Date(user.created_at).toLocaleDateString();
            const roleColor = user.role === 'Owner' ? 'text-yellow-400' : 'text-blue-400';
            const roleIcon = user.role === 'Owner' ? 'fa-crown' : 'fa-user';

            detailsHTML += `
                <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg border border-gray-800">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                            <i class="fas ${roleIcon} text-[#00BFFF] text-sm"></i>
                        </div>
                        <div>
                            <p class="text-white font-medium">${user.full_name || 'Unknown User'}</p>
                            <p class="text-xs text-gray-500">Joined ${joinedDate}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="px-2 py-1 rounded-full text-xs font-medium ${roleColor} bg-gray-800">
                            ${user.role}
                        </span>
                    </div>
                </div>
            `;
        });

        detailsHTML += `</div>`;
    }

    detailsHTML += `
            </div>

            <!-- Actions -->
            <div class="pt-4 border-t border-gray-700">
                <div class="flex flex-col space-y-3">
                    <button onclick="handleEditCompany('${company.id}', '${company.name}')"
                            class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm transition-colors flex items-center justify-center space-x-2">
                        <i class="fas fa-edit"></i>
                        <span>Edit Company</span>
                    </button>
                    <button onclick="handleDeleteCompany('${company.id}', '${company.name}', ${userCount})"
                            class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors flex items-center justify-center space-x-2">
                        <i class="fas fa-trash"></i>
                        <span>Delete Company</span>
                    </button>
                </div>
                <p class="text-xs text-gray-500 mt-3 text-center">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    Deleting a company will permanently remove all associated users and data.
                </p>
            </div>
        </div>
    `;

    return detailsHTML;
}

// Function to load detailed company information and users (legacy - kept for compatibility)
async function loadCompanyDetails(cardElement, company) {
    // For the new layout, just select the company
    await selectCompany(cardElement, company);
}



// Function to load users for a specific company
async function loadCompanyUsers(companyName) {
    console.log('Loading users for company:', companyName);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        // Query users by company name
        const { data, error } = await supabase
            .from('user_profiles')
            .select('user_id, full_name, role, created_at')
            .eq('company_name', companyName)
            .order('created_at', { ascending: true });

        if (error) {
            console.error('Error loading company users:', error);
            throw error;
        }

        return data || [];

    } catch (error) {
        console.error('Error in loadCompanyUsers:', error);
        throw error;
    }
}

// Function to handle company deletion with cascading user removal
async function handleDeleteCompany(companyId, companyName, userCount) {
    console.log('Delete company requested:', companyId, companyName, 'with', userCount, 'users');

    // First confirmation - basic deletion warning
    const firstConfirm = confirm(
        `⚠️ DELETE COMPANY WARNING ⚠️\n\n` +
        `Company: "${companyName}"\n` +
        `Connected Users: ${userCount}\n\n` +
        `This action will permanently delete:\n` +
        `• The company "${companyName}"\n` +
        `• All ${userCount} associated user accounts (including login credentials)\n` +
        `• All user authentication data from Supabase\n` +
        `• All user integrations and personal data\n` +
        `• All AI agents for this company\n\n` +
        `⚠️ Users will lose access to their accounts permanently!\n` +
        `This action CANNOT be undone!\n\n` +
        `Are you sure you want to continue?`
    );

    if (!firstConfirm) {
        console.log('Company deletion cancelled by user (first confirmation)');
        return;
    }

    // Second confirmation - type company name to confirm
    const typedName = prompt(
        `🚨 FINAL CONFIRMATION 🚨\n\n` +
        `To confirm deletion, please type the company name exactly:\n\n` +
        `"${companyName}"\n\n` +
        `Type the company name to proceed with deletion:`
    );

    if (typedName !== companyName) {
        if (typedName !== null) { // User didn't cancel, but typed wrong name
            showToast('Company name does not match. Deletion cancelled.', 'error');
        }
        console.log('Company deletion cancelled - name mismatch or user cancelled');
        return;
    }

    // Proceed with deletion
    try {
        showToast('Deleting company and all associated data...', 'info');

        await performCompanyDeletion(companyId, companyName);

        showToast(`Company "${companyName}" and all associated data deleted successfully!`, 'success');

        // Refresh the companies list
        await populateCompaniesManagement();

    } catch (error) {
        console.error('Error deleting company:', error);
        showToast(`Failed to delete company: ${error.message}`, 'error');
    }
}

// Function to perform the actual company deletion with cascading
async function performCompanyDeletion(companyId, companyName) {
    console.log('Performing company deletion:', companyId, companyName);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        // Step 1: Get all users associated with this company
        const { data: companyUsers, error: usersError } = await supabase
            .from('user_profiles')
            .select('user_id')
            .eq('company_name', companyName);

        if (usersError) {
            throw new Error(`Failed to fetch company users: ${usersError.message}`);
        }

        console.log('Found users to delete:', companyUsers);

        // Step 2: Delete user integrations for all company users
        if (companyUsers && companyUsers.length > 0) {
            const userIds = companyUsers.map(user => user.user_id);

            const { error: integrationsError } = await supabase
                .from('user_integrations')
                .delete()
                .in('user_id', userIds);

            if (integrationsError) {
                console.warn('Error deleting user integrations:', integrationsError);
                // Continue with deletion even if integrations fail
            }
        }

        // Step 3: Delete AI agents for this company
        const { error: agentsError } = await supabase
            .from('ai_agents')
            .delete()
            .eq('company_name', companyName);

        if (agentsError) {
            console.warn('Error deleting AI agents:', agentsError);
            // Continue with deletion even if agents fail
        }

        // Step 4: Delete users from Supabase auth table
        if (companyUsers && companyUsers.length > 0) {
            console.log('Deleting auth users for company:', companyName);

            for (const user of companyUsers) {
                try {
                    // Delete user from auth.users table using admin API
                    const { error: authDeleteError } = await supabase.auth.admin.deleteUser(user.user_id);

                    if (authDeleteError) {
                        console.warn(`Failed to delete auth user ${user.user_id}:`, authDeleteError);
                        // Continue with other users even if one fails
                    } else {
                        console.log(`Successfully deleted auth user: ${user.user_id}`);
                    }
                } catch (authError) {
                    console.warn(`Error deleting auth user ${user.user_id}:`, authError);
                    // Continue with other users even if one fails
                }
            }
        }

        // Step 5: Delete user profiles (cleanup any remaining profile records)
        const { error: profilesError } = await supabase
            .from('user_profiles')
            .delete()
            .eq('company_name', companyName);

        if (profilesError) {
            console.warn('Error deleting user profiles:', profilesError);
            // Continue with company deletion even if profiles fail to delete
        }

        // Step 6: Finally delete the company
        const { error: companyError } = await supabase
            .from('companies')
            .delete()
            .eq('id', companyId);

        if (companyError) {
            throw new Error(`Failed to delete company: ${companyError.message}`);
        }

        console.log('Company deletion completed successfully');

        // Trigger immediate statistics update
        if (typeof triggerStatisticsUpdate === 'function') {
            triggerStatisticsUpdate();
        }

    } catch (error) {
        console.error('Error in performCompanyDeletion:', error);
        throw error;
    }
}

// Function to handle company editing (placeholder for future implementation)
async function handleEditCompany(companyId, companyName) {
    console.log('Edit company requested:', companyId, companyName);
    showToast('Company editing functionality coming soon!', 'info');
}

// Function to reset company details panel to empty state
async function resetCompanyDetailsPanel() {
    const emptyState = document.getElementById('company-details-empty');
    const contentElement = document.getElementById('company-details-content');

    if (emptyState && contentElement) {
        // If content is visible, animate it out first
        if (!contentElement.classList.contains('hidden')) {
            contentElement.classList.add('company-details-exiting');
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        // Show empty state and hide content
        emptyState.classList.remove('hidden');
        contentElement.classList.add('hidden');
        contentElement.classList.remove('company-details-exiting', 'company-details-entering', 'company-details-entered');
        contentElement.innerHTML = '';
    }
}

// Function to setup company search functionality
function setupCompanySearch() {
    const searchInput = document.getElementById('company-search-input');
    const clearButton = document.getElementById('clear-company-search-btn');

    if (!searchInput || !clearButton) {
        console.warn('Company search elements not found');
        return;
    }

    // Add event listeners
    searchInput.addEventListener('input', handleCompanySearch);
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            clearCompanySearch();
        }
    });
    clearButton.addEventListener('click', clearCompanySearch);

    console.log('Company search functionality setup complete');
}

// Function to handle company search
function handleCompanySearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();
    const clearButton = document.getElementById('clear-company-search-btn');
    const companyCards = document.querySelectorAll('.company-management-card');

    // Show/hide clear button
    if (searchTerm) {
        clearButton.classList.remove('hidden');
    } else {
        clearButton.classList.add('hidden');
    }

    // Filter company cards
    let visibleCount = 0;
    companyCards.forEach(card => {
        const companyName = card.dataset.companyName?.toLowerCase() || '';

        const matchesSearch = companyName.includes(searchTerm) || searchTerm === '';

        if (matchesSearch) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
            // If this card was selected, reset the details panel
            if (card.classList.contains('selected')) {
                card.classList.remove('selected');
                resetCompanyDetailsPanel();
            }
        }
    });

    // Show no results message if needed
    showNoCompanySearchResults(visibleCount === 0 && searchTerm !== '');
}

// Function to clear company search
function clearCompanySearch() {
    const searchInput = document.getElementById('company-search-input');
    const clearButton = document.getElementById('clear-company-search-btn');

    if (searchInput) {
        searchInput.value = '';
    }

    if (clearButton) {
        clearButton.classList.add('hidden');
    }

    // Show all company cards
    const companyCards = document.querySelectorAll('.company-management-card');
    companyCards.forEach(card => {
        card.style.display = 'block';
    });

    // Hide no results message
    showNoCompanySearchResults(false);
}

// Function to show/hide no search results message for companies
function showNoCompanySearchResults(show) {
    const container = document.getElementById('companies-management-container');
    let noResultsElement = container.querySelector('.no-company-search-results');

    if (show) {
        if (!noResultsElement) {
            noResultsElement = document.createElement('div');
            noResultsElement.className = 'no-company-search-results flex items-center justify-center p-8 text-center';
            noResultsElement.innerHTML = `
                <div>
                    <i class="fas fa-search text-3xl text-gray-500 mb-3"></i>
                    <h4 class="text-lg font-medium text-gray-300 mb-2">No Companies Found</h4>
                    <p class="text-sm text-gray-400 mb-4">No companies match your search criteria</p>
                    <button onclick="clearCompanySearch()" class="px-4 py-2 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-md transition-colors flex items-center space-x-2 mx-auto">
                        <i class="fas fa-times mr-2"></i>
                        <span>Clear Search</span>
                    </button>
                </div>
            `;
            container.appendChild(noResultsElement);
        }
        noResultsElement.style.display = 'block';
    } else {
        if (noResultsElement) {
            noResultsElement.style.display = 'none';
        }
    }
}

// Client Management Functions

// Function to populate clients in the management view
async function populateClientsManagement() {
    console.log('populateClientsManagement called');

    const container = document.getElementById('clients-management-container');
    const loadingElement = document.getElementById('clients-management-loading');

    if (!container) {
        console.warn('Clients management container not found');
        return;
    }

    console.log('Clients management container found, loading clients...');

    // Clear search when refreshing
    clearClientSearch();

    try {
        const clients = await loadClients();
        console.log('Clients loaded for management:', clients);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Clear any existing cards (except loading)
        const existingCards = container.querySelectorAll('.client-management-card');
        existingCards.forEach(card => card.remove());

        if (clients.length === 0) {
            // Show empty state
            const emptyState = document.createElement('div');
            emptyState.className = 'flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg col-span-full';
            emptyState.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-users text-4xl text-gray-500 mb-4"></i>
                    <h4 class="text-lg font-medium text-gray-300 mb-2">No Clients Found</h4>
                    <p class="text-sm text-gray-400">No client users found in your organization</p>
                </div>
            `;
            container.appendChild(emptyState);
            return;
        }

        // Create client management cards
        clients.forEach((client, index) => {
            console.log(`Creating management card for client ${index + 1}:`, client);

            const card = document.createElement('div');
            card.className = 'client-management-card';
            card.dataset.clientId = client.user_id;
            card.dataset.clientName = client.full_name;

            const joinedDate = new Date(client.created_at || Date.now()).toLocaleDateString();

            card.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-[#00BFFF] to-[#0080FF] rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-semibold text-white truncate">${client.full_name || 'Unknown User'}</h4>
                        <p class="text-xs text-gray-400 truncate">${client.company_name || 'No Company'}</p>
                        <p class="text-xs text-gray-500">Joined: ${joinedDate}</p>
                    </div>
                </div>
            `;

            // Add click handler for selection
            card.addEventListener('click', () => {
                selectClient(card, client);
            });

            container.appendChild(card);
        });

        console.log('Client management cards created successfully!');

        // Reset the details panel to empty state
        resetClientDetailsPanel();

        // Setup search functionality
        setupClientSearch();

    } catch (error) {
        console.error('Error populating client management:', error);

        // Hide loading state
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Show error state
        const errorState = document.createElement('div');
        errorState.className = 'flex items-center justify-center p-8 bg-red-900/20 border border-red-700 rounded-lg';
        errorState.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-3xl text-red-400 mb-2"></i>
                <p class="text-red-300 mb-2">Error loading clients</p>
                <button onclick="populateClientsManagement()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Retry
                </button>
            </div>
        `;
        container.appendChild(errorState);

        // Reset the details panel to empty state
        resetClientDetailsPanel();
    }
}

// Function to reset client details panel to empty state
async function resetClientDetailsPanel() {
    const emptyState = document.getElementById('client-details-empty');
    const contentElement = document.getElementById('client-details-content');

    if (emptyState && contentElement) {
        // If content is visible, animate it out first
        if (!contentElement.classList.contains('hidden')) {
            contentElement.classList.add('client-details-exiting');
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        // Show empty state and hide content
        emptyState.classList.remove('hidden');
        contentElement.classList.add('hidden');
        contentElement.classList.remove('client-details-exiting', 'client-details-entering', 'client-details-entered');
        contentElement.innerHTML = '';
    }
}

// Function to setup client search functionality
function setupClientSearch() {
    const searchInput = document.getElementById('client-search-input');
    const clearButton = document.getElementById('clear-search-btn');

    if (!searchInput || !clearButton) {
        console.warn('Search elements not found');
        return;
    }

    // Add event listeners
    searchInput.addEventListener('input', handleClientSearch);
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            clearClientSearch();
        }
    });
    clearButton.addEventListener('click', clearClientSearch);

    console.log('Client search functionality setup complete');
}

// Function to handle client search
function handleClientSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();
    const clearButton = document.getElementById('clear-search-btn');
    const clientCards = document.querySelectorAll('.client-management-card');

    // Show/hide clear button
    if (searchTerm) {
        clearButton.classList.remove('hidden');
    } else {
        clearButton.classList.add('hidden');
    }

    // Filter client cards
    let visibleCount = 0;
    clientCards.forEach(card => {
        const clientName = card.dataset.clientName?.toLowerCase() || '';
        const clientCompany = card.querySelector('.text-gray-400')?.textContent?.toLowerCase() || '';

        const matchesSearch = clientName.includes(searchTerm) ||
                            clientCompany.includes(searchTerm) ||
                            searchTerm === '';

        if (matchesSearch) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
            // If this card was selected, reset the details panel
            if (card.classList.contains('selected')) {
                card.classList.remove('selected');
                resetClientDetailsPanel();
            }
        }
    });

    // Show no results message if needed
    showNoSearchResults(visibleCount === 0 && searchTerm !== '');
}

// Function to clear client search
function clearClientSearch() {
    const searchInput = document.getElementById('client-search-input');
    const clearButton = document.getElementById('clear-search-btn');

    if (searchInput) {
        searchInput.value = '';
    }

    if (clearButton) {
        clearButton.classList.add('hidden');
    }

    // Show all client cards
    const clientCards = document.querySelectorAll('.client-management-card');
    clientCards.forEach(card => {
        card.style.display = 'block';
    });

    // Hide no results message
    showNoSearchResults(false);
}

// Function to show/hide no search results message
function showNoSearchResults(show) {
    const container = document.getElementById('clients-management-container');
    let noResultsElement = container.querySelector('.no-search-results');

    if (show) {
        if (!noResultsElement) {
            noResultsElement = document.createElement('div');
            noResultsElement.className = 'no-search-results flex items-center justify-center p-8 text-center';
            noResultsElement.innerHTML = `
                <div>
                    <i class="fas fa-search text-3xl text-gray-500 mb-3"></i>
                    <h4 class="text-lg font-medium text-gray-300 mb-2">No Clients Found</h4>
                    <p class="text-sm text-gray-400 mb-4">No clients match your search criteria</p>
                    <button onclick="clearClientSearch()" class="px-4 py-2 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-md transition-colors flex items-center space-x-2 mx-auto">
                        <i class="fas fa-times mr-2"></i>
                        <span>Clear Search</span>
                    </button>
                </div>
            `;
            container.appendChild(noResultsElement);
        }
        noResultsElement.style.display = 'block';
    } else {
        if (noResultsElement) {
            noResultsElement.style.display = 'none';
        }
    }
}

// Function to load clients from database
async function loadClients() {
    console.log('Loading clients...');

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        // Query users with Client role
        const { data, error } = await supabase
            .from('user_profiles')
            .select('user_id, full_name, company_name, role, created_at')
            .eq('role', 'Client')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error loading clients:', error);
            throw error;
        }

        return data || [];

    } catch (error) {
        console.error('Error in loadClients:', error);
        throw error;
    }
}

// Function to select a client and show details in right panel
async function selectClient(cardElement, client) {
    console.log('Selecting client:', client.full_name);

    // Add selecting animation to clicked card
    cardElement.classList.add('selecting');

    // Remove selection from all cards
    const allCards = document.querySelectorAll('.client-management-card');
    allCards.forEach(card => {
        card.classList.remove('selected');
        if (card !== cardElement) {
            card.classList.remove('selecting');
        }
    });

    // Show client details in right panel with animation
    await showClientDetailsInPanel(client);

    // Complete the selection animation
    setTimeout(() => {
        cardElement.classList.remove('selecting');
        cardElement.classList.add('selected');
    }, 200);
}

// Function to show client details in the right panel
async function showClientDetailsInPanel(client) {
    console.log('Showing client details in panel for:', client.full_name);

    const emptyState = document.getElementById('client-details-empty');
    const contentElement = document.getElementById('client-details-content');

    // Start exit animation if content is already visible
    if (!contentElement.classList.contains('hidden')) {
        contentElement.classList.add('client-details-exiting');
        await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Hide empty state and show content
    emptyState.classList.add('hidden');
    contentElement.classList.remove('hidden');
    contentElement.classList.remove('client-details-exiting');
    contentElement.classList.add('client-details-entering');

    // Show loading state with animation
    contentElement.innerHTML = `
        <div class="client-details-loading flex items-center justify-center py-8">
            <div class="flex items-center space-x-3">
                <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                <span class="text-gray-300">Loading client details...</span>
            </div>
        </div>
    `;

    try {
        // Add a small delay to make the loading state visible
        await new Promise(resolve => setTimeout(resolve, 300));

        // Load additional client information
        const clientIntegrations = await loadClientIntegrations(client.user_id);
        console.log('Client integrations loaded:', clientIntegrations);

        // Build the details content
        const detailsHTML = buildClientDetailsHTML(client, clientIntegrations);

        // Animate content in
        contentElement.innerHTML = detailsHTML;

        // Trigger enter animation
        setTimeout(() => {
            contentElement.classList.remove('client-details-entering');
            contentElement.classList.add('client-details-entered');
        }, 50);

        // Clean up animation classes after animation completes
        setTimeout(() => {
            contentElement.classList.remove('client-details-entered');
        }, 450);

    } catch (error) {
        console.error('Error loading client details:', error);

        // Show error state with animation
        contentElement.innerHTML = `
            <div class="text-center py-8 bg-red-900/20 rounded-lg border border-red-700">
                <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                <p class="text-red-300 mb-2">Error loading client details</p>
                <button onclick="showClientDetailsInPanel(${JSON.stringify(client).replace(/"/g, '&quot;')})"
                        class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors">
                    <i class="fas fa-retry mr-1"></i>Retry
                </button>
            </div>
        `;

        // Trigger enter animation for error state
        setTimeout(() => {
            contentElement.classList.remove('client-details-entering');
            contentElement.classList.add('client-details-entered');
        }, 50);

        setTimeout(() => {
            contentElement.classList.remove('client-details-entered');
        }, 450);
    }
}

// Function to toggle client details expansion (legacy - kept for compatibility)
async function toggleClientDetails(cardElement, client) {
    // For the new layout, just select the client
    await selectClient(cardElement, client);
}

// Function to build client details HTML
function buildClientDetailsHTML(client, clientIntegrations) {
    const joinedDate = new Date(client.created_at || Date.now()).toLocaleDateString();

    let detailsHTML = `
        <div class="space-y-6">
            <!-- Client Header -->
            <div class="flex items-center space-x-4 pb-4 border-b border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-[#00BFFF] to-[#0080FF] rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-xl"></i>
                </div>
                <div>
                    <h4 class="text-xl font-semibold text-white">${client.full_name || 'Unknown User'}</h4>
                    <p class="text-gray-400">${client.company_name || 'No Company'}</p>
                    <p class="text-sm text-gray-500">Joined: ${joinedDate}</p>
                </div>
            </div>

            <!-- Client Information -->
            <div>
                <h5 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-info-circle text-[#00BFFF] mr-2"></i>
                    Client Information
                </h5>
                <div class="grid grid-cols-1 gap-3 text-sm">
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Full Name:</span>
                        <p class="text-white font-medium mt-1">${client.full_name || 'Not specified'}</p>
                    </div>
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Company:</span>
                        <p class="text-white font-medium mt-1">${client.company_name || 'Not specified'}</p>
                    </div>
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">Role:</span>
                        <p class="text-white font-medium mt-1">
                            <span class="px-2 py-1 rounded-full text-xs font-medium text-blue-400 bg-gray-800">${client.role}</span>
                        </p>
                    </div>
                    <div class="bg-[#1A1A1A] p-3 rounded-lg border border-gray-800">
                        <span class="text-gray-400">User ID:</span>
                        <p class="text-gray-300 font-mono text-xs mt-1 break-all">${client.user_id}</p>
                    </div>
                </div>
            </div>

            <!-- Integrations Section -->
            <div>
                <h5 class="text-white font-medium mb-3 flex items-center">
                    <i class="fas fa-plug text-[#00BFFF] mr-2"></i>
                    Integrations (${clientIntegrations.length})
                </h5>
    `;

    if (clientIntegrations.length === 0) {
        detailsHTML += `
                <div class="text-center py-6 bg-[#1A1A1A] rounded-lg border border-gray-800">
                    <i class="fas fa-plug text-gray-500 text-2xl mb-2"></i>
                    <p class="text-gray-400 text-sm">No integrations connected</p>
                </div>
        `;
    } else {
        detailsHTML += `<div class="space-y-2">`;

        clientIntegrations.forEach(integration => {
            const connectedDate = new Date(integration.connected_at || integration.updated_at).toLocaleDateString();
            const statusColor = integration.status === 'connected' ? 'text-green-400' : 'text-gray-400';
            const statusIcon = integration.status === 'connected' ? 'fa-check-circle' : 'fa-times-circle';

            detailsHTML += `
                <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg border border-gray-800">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                            <i class="fas fa-plug text-[#00BFFF] text-sm"></i>
                        </div>
                        <div>
                            <p class="text-white font-medium capitalize">${integration.service_name}</p>
                            <p class="text-xs text-gray-500">Connected ${connectedDate}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center space-x-2">
                            <i class="fas ${statusIcon} ${statusColor}"></i>
                            <span class="text-xs ${statusColor} capitalize">${integration.status}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        detailsHTML += `</div>`;
    }

    detailsHTML += `
            </div>

            <!-- Actions -->
            <div class="pt-4 border-t border-gray-700">
                <div class="flex space-x-3">
                    <button onclick="handleChangeClientRole('${client.user_id}', '${client.full_name}')"
                            class="flex-1 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-md text-sm transition-colors flex items-center justify-center space-x-2">
                        <i class="fas fa-user-cog"></i>
                        <span>Change Role to Owner</span>
                    </button>
                    <button onclick="handleRemoveClient('${client.user_id}', '${client.full_name}')"
                            class="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors flex items-center justify-center space-x-2">
                        <i class="fas fa-user-times"></i>
                        <span>Remove Client</span>
                    </button>
                </div>
            </div>
        </div>
    `;

    return detailsHTML;
}

// Function to load detailed client information (legacy - kept for compatibility)
async function loadClientDetails(cardElement, client) {
    // For the new layout, just select the client
    await selectClient(cardElement, client);
}

// Function to load client integrations
async function loadClientIntegrations(userId) {
    console.log('Loading integrations for client:', userId);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        // Query user integrations - only show connected ones in client details
        const { data, error } = await supabase
            .from('user_integrations')
            .select('service_name, status, connected_at, updated_at')
            .eq('user_id', userId)
            .eq('status', 'connected')  // Only show connected integrations
            .order('updated_at', { ascending: false });

        if (error) {
            console.error('Error loading client integrations:', error);
            throw error;
        }

        return data || [];

    } catch (error) {
        console.error('Error in loadClientIntegrations:', error);
        return []; // Return empty array on error to prevent breaking the UI
    }
}

// Function to handle changing client role
async function handleChangeClientRole(userId, clientName) {
    console.log('Change role requested for client:', userId, clientName);

    const confirmed = confirm(`Are you sure you want to change the role of "${clientName}" from Client to Owner?\n\nThis will give them full administrative access to the system.`);

    if (confirmed) {
        try {
            showToast('Updating client role...', 'info');

            const supabase = getSupabaseClient();
            if (!supabase) {
                throw new Error('Database connection not available');
            }

            // Update user role to Owner
            const { error } = await supabase
                .from('user_profiles')
                .update({
                    role: 'Owner',
                    updated_at: new Date().toISOString()
                })
                .eq('user_id', userId);

            if (error) {
                throw error;
            }

            showToast(`Successfully changed "${clientName}" role to Owner!`, 'success');

            // Refresh the clients list
            await populateClientsManagement();

        } catch (error) {
            console.error('Error changing client role:', error);
            showToast(`Failed to change client role: ${error.message}`, 'error');
        }
    }
}

// Function to handle removing client
async function handleRemoveClient(userId, clientName) {
    console.log('Remove client requested:', userId, clientName);

    const confirmed = confirm(`Are you sure you want to remove "${clientName}" from the system?\n\nThis action cannot be undone and will delete all their data.`);

    if (confirmed) {
        try {
            showToast('Removing client...', 'info');

            const supabase = getSupabaseClient();
            if (!supabase) {
                throw new Error('Database connection not available');
            }

            // Delete user profile (this should cascade to other related data)
            const { error } = await supabase
                .from('user_profiles')
                .delete()
                .eq('user_id', userId);

            if (error) {
                throw error;
            }

            showToast(`Successfully removed "${clientName}" from the system!`, 'success');

            // Trigger immediate statistics update
            if (typeof triggerStatisticsUpdate === 'function') {
                triggerStatisticsUpdate();
            }

            // Refresh the clients list
            await populateClientsManagement();

        } catch (error) {
            console.error('Error removing client:', error);
            showToast(`Failed to remove client: ${error.message}`, 'error');
        }
    }
}

// Integrations Page Functions

// Initialize integrations page
async function initializeIntegrationsPage() {
    console.log('initializeIntegrationsPage called');

    // Load user's integration status
    await loadIntegrationStatuses();

    // Setup event listeners for connect buttons
    setupIntegrationEventListeners();
}

// Load integration statuses from database
async function loadIntegrationStatuses() {
    console.log('Loading integration statuses...');

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        // Query user integrations
        const { data: integrations, error } = await supabase
            .from('user_integrations')
            .select('*')
            .eq('user_id', user.id);

        if (error) {
            console.error('Error loading integrations:', error);
            return;
        }

        console.log('User integrations loaded:', integrations);

        // Update UI based on integration statuses
        updateIntegrationUI(integrations || []);

    } catch (error) {
        console.error('Error in loadIntegrationStatuses:', error);
        // Don't show error to user, just log it
    }
}

// Update integration UI based on status
function updateIntegrationUI(integrations) {
    console.log('Updating integration UI with data:', integrations);

    // Create a map of integration data
    const integrationMap = {};
    integrations.forEach(integration => {
        integrationMap[integration.service_name] = integration;
    });

    // Update each integration card
    const integrationCards = document.querySelectorAll('.integration-card');
    integrationCards.forEach(card => {
        const connectBtn = card.querySelector('.connect-btn');
        const disconnectBtn = card.querySelector('.disconnect-btn');
        const statusBadge = card.querySelector('.status-badge');
        const connectionDetails = card.querySelector('.connection-details');
        const integrationName = connectBtn?.getAttribute('data-integration');

        if (!integrationName || !connectBtn || !statusBadge) return;

        const integrationData = integrationMap[integrationName];
        const status = integrationData?.status || 'disconnected';

        // Update status badge
        statusBadge.className = 'status-badge px-2 py-1 rounded-full text-xs';

        // Update buttons and badge based on status
        switch (status) {
            case 'connected':
                statusBadge.classList.add('bg-green-600', 'text-white');
                statusBadge.textContent = 'Connected';
                connectBtn.classList.add('connected', 'hidden');
                connectBtn.innerHTML = '<i class="fas fa-check mr-1"></i>Connected';
                connectBtn.disabled = true;
                if (disconnectBtn) {
                    disconnectBtn.classList.remove('hidden');
                    disconnectBtn.disabled = false;
                }

                // Show connection details for connected integrations
                if (connectionDetails && integrationData) {
                    updateConnectionDetails(card, integrationData);
                    connectionDetails.classList.remove('hidden');
                }
                break;

            case 'connecting':
                statusBadge.classList.add('bg-yellow-600', 'text-white');
                statusBadge.textContent = 'Connecting...';
                connectBtn.classList.add('connecting');
                connectBtn.classList.remove('hidden');
                connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Connecting...';
                connectBtn.disabled = true;
                if (disconnectBtn) {
                    disconnectBtn.classList.add('hidden');
                    disconnectBtn.disabled = true;
                }
                if (connectionDetails) {
                    connectionDetails.classList.add('hidden');
                }
                break;

            case 'disconnecting':
                statusBadge.classList.add('bg-red-600', 'text-white');
                statusBadge.textContent = 'Disconnecting...';
                connectBtn.classList.add('hidden');
                connectBtn.disabled = true;
                if (disconnectBtn) {
                    disconnectBtn.classList.remove('hidden');
                    disconnectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Disconnecting...';
                    disconnectBtn.disabled = true;
                }
                if (connectionDetails) {
                    connectionDetails.classList.add('hidden');
                }
                break;

            default:
                statusBadge.classList.add('bg-gray-600', 'text-gray-300');
                statusBadge.textContent = 'Disconnected';
                connectBtn.classList.remove('connected', 'connecting', 'hidden');
                connectBtn.innerHTML = `<i class="fas fa-plug mr-1"></i>Connect`;
                connectBtn.disabled = false;
                if (disconnectBtn) {
                    disconnectBtn.classList.add('hidden');
                    disconnectBtn.disabled = true;
                    disconnectBtn.innerHTML = `<i class="fas fa-unlink mr-1"></i>Disconnect`;
                }
                if (connectionDetails) {
                    connectionDetails.classList.add('hidden');
                }
        }
    });
}

// Update connection details for a specific integration card
function updateConnectionDetails(card, integrationData) {
    const connectedEmail = card.querySelector('.connected-email');
    const connectedDate = card.querySelector('.connected-date');

    if (connectedEmail && integrationData.metadata?.email) {
        connectedEmail.textContent = integrationData.metadata.email;
    }

    if (connectedDate && integrationData.connected_at) {
        const date = new Date(integrationData.connected_at);
        connectedDate.textContent = date.toLocaleDateString();
    }
}

// Setup event listeners for integration buttons
function setupIntegrationEventListeners() {
    console.log('Setting up integration event listeners...');

    // Use event delegation for connect buttons
    const connectButtons = document.querySelectorAll('.connect-btn');
    connectButtons.forEach(button => {
        button.addEventListener('click', handleIntegrationConnect);
    });

    // Use document-level event delegation for disconnect buttons (since they might be hidden/shown dynamically)
    // Remove duplicate event listeners to prevent multiple disconnect events
    document.addEventListener('click', function(event) {
        if (event.target.closest('.disconnect-btn')) {
            console.log('Disconnect button clicked via document event delegation');
            handleIntegrationDisconnect(event);
        }
    });

    // Note: Removed direct event listeners to prevent duplicate events
    console.log('Integration event listeners set up with single event delegation');
}

// Handle integration connection
async function handleIntegrationConnect(event) {
    const button = event.target.closest('.connect-btn');
    const integration = button.getAttribute('data-integration');

    console.log('Integration connect clicked:', integration);

    // Don't proceed if already connected or connecting
    if (button.classList.contains('connected') || button.classList.contains('connecting')) {
        return;
    }

    // Set connecting state
    setIntegrationConnecting(integration, true);

    try {
        // Handle Gmail integration only
        if (integration === 'gmail') {
            await handleGmailOAuth2Connection();
        } else {
            // Only Gmail is supported
            throw new Error(`Integration ${integration} is not supported yet. Only Gmail integration is currently available.`);
        }

        // Update to connected state
        await updateIntegrationStatus(integration, 'connected');

        const displayName = getIntegrationDisplayName(integration);
        showToast(`${displayName} connected successfully!`, 'success');

        // Note: Activity logging is handled automatically by database trigger
        // Refresh notifications if on notifications page
        await refreshNotificationsIfVisible();

    } catch (error) {
        console.error('Integration connection error:', error);
        const displayName = getIntegrationDisplayName(integration);
        showToast(`Failed to connect ${displayName}. Please try again.`, 'error');

        // Reset to disconnected state
        setIntegrationConnecting(integration, false);
    }
}

// Handle integration disconnection
async function handleIntegrationDisconnect(event) {
    console.log('handleIntegrationDisconnect called', event);

    const button = event.target.closest('.disconnect-btn');
    if (!button) {
        console.error('No disconnect button found in event target');
        return;
    }

    const integration = button.getAttribute('data-integration');
    if (!integration) {
        console.error('No data-integration attribute found on button');
        return;
    }

    // Prevent multiple rapid clicks
    if (button.disabled || button.classList.contains('disconnecting')) {
        console.log('Disconnect already in progress, ignoring click');
        return;
    }

    console.log('Integration disconnect clicked:', integration);

    const displayName = getIntegrationDisplayName(integration);

    // Set disconnecting state immediately (no confirmation dialog)
    setIntegrationDisconnecting(integration, true);

    try {
        // Simulate disconnection process (in real implementation, this would revoke tokens, etc.)
        await simulateIntegrationDisconnection(integration);

        // Update to disconnected state
        await updateIntegrationStatus(integration, 'disconnected');

        showToast(`${displayName} disconnected successfully!`, 'success');

        // Note: Activity logging is handled automatically by database trigger
        // Refresh notifications if on notifications page
        await refreshNotificationsIfVisible();

    } catch (error) {
        console.error('Integration disconnection error:', error);
        showToast(`Failed to disconnect ${displayName}. Please try again.`, 'error');

        // Reset to connected state on error
        setIntegrationDisconnecting(integration, false);
        await loadIntegrationStatuses(); // Refresh to show correct state
    }
}

// Get display name for integration - Gmail only
function getIntegrationDisplayName(integration) {
    const names = {
        'gmail': 'Gmail'
    };
    return names[integration] || integration.charAt(0).toUpperCase() + integration.slice(1);
}

// Set integration connecting state
function setIntegrationConnecting(integration, isConnecting) {
    const element = document.querySelector(`[data-integration="${integration}"]`);
    if (!element) {
        console.warn(`Integration element not found for: ${integration}`);
        return;
    }

    const card = element.closest('.integration-card');
    if (!card) {
        console.warn(`Integration card not found for: ${integration}`);
        return;
    }

    const button = card.querySelector('.connect-btn');
    const statusBadge = card.querySelector('.status-badge');

    if (!button || !statusBadge) {
        console.warn(`Button or status badge not found for: ${integration}`);
        return;
    }

    if (isConnecting) {
        statusBadge.className = 'status-badge connecting px-2 py-1 rounded-full text-xs';
        statusBadge.textContent = 'Connecting...';
        button.classList.add('connecting');
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Connecting...';
        button.disabled = true;
    } else {
        statusBadge.className = 'status-badge bg-gray-600 text-gray-300 px-2 py-1 rounded-full text-xs';
        statusBadge.textContent = 'Not Connected';
        button.classList.remove('connecting');
        button.innerHTML = `<i class="fas fa-plug mr-2"></i>Connect ${integration.charAt(0).toUpperCase() + integration.slice(1)}`;
        button.disabled = false;
    }
}

// Set integration disconnecting state
function setIntegrationDisconnecting(integration, isDisconnecting) {
    const element = document.querySelector(`[data-integration="${integration}"]`);
    if (!element) {
        console.warn(`Integration element not found for: ${integration}`);
        return;
    }

    const card = element.closest('.integration-card');
    if (!card) {
        console.warn(`Integration card not found for: ${integration}`);
        return;
    }

    const connectBtn = card.querySelector('.connect-btn');
    const disconnectBtn = card.querySelector('.disconnect-btn');
    const statusBadge = card.querySelector('.status-badge');

    if (!connectBtn || !disconnectBtn || !statusBadge) {
        console.warn(`Required elements not found for: ${integration}`);
        return;
    }

    if (isDisconnecting) {
        statusBadge.className = 'status-badge disconnecting px-2 py-1 rounded-full text-xs';
        statusBadge.textContent = 'Disconnecting...';
        connectBtn.classList.add('hidden');
        connectBtn.disabled = true;
        if (disconnectBtn) {
            disconnectBtn.classList.remove('hidden');
            disconnectBtn.classList.add('disconnecting');
            disconnectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Disconnecting...';
            disconnectBtn.disabled = true;
        }
    } else {
        // Reset to connected state
        statusBadge.className = 'status-badge connected px-2 py-1 rounded-full text-xs';
        statusBadge.textContent = 'Connected';
        connectBtn.classList.add('connected', 'hidden');
        connectBtn.disabled = true;
        if (disconnectBtn) {
            disconnectBtn.classList.remove('hidden', 'disconnecting');
            const displayName = getIntegrationDisplayName(integration);
            disconnectBtn.innerHTML = `<i class="fas fa-unlink mr-2"></i>Disconnect ${displayName}`;
            disconnectBtn.disabled = false;
        }
    }
}

// Simulate integration connection (replace with actual OAuth flows)
async function simulateIntegrationConnection(integration) {
    console.log(`Simulating connection to ${integration}...`);

    // Handle different integration types
    switch (integration) {
        case 'gmail':
            await simulateGmailConnection();
            break;
        default:
            throw new Error(`Integration ${integration} is not supported yet`);
    }
}

// Gmail webhook connection
async function simulateGmailConnection() {
    console.log('Triggering Gmail integration webhook...');

    // Trigger the webhook
    await triggerGmailWebhook();

    // Store the connection data
    await storeGmailConnectionData();
}

// Gmail Webhook Configuration
const GMAIL_WEBHOOK_CONFIG = {
    webhookUrl: 'https://primary-production-db258.up.railway.app/webhook/************************************'
};

// Trigger Gmail integration webhook
async function triggerGmailWebhook() {
    try {
        console.log('Triggering Gmail webhook...');

        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        // Prepare webhook payload
        const webhookPayload = {
            event: 'gmail_integration_request',
            user_id: user.id,
            user_email: user.email,
            timestamp: new Date().toISOString(),
            integration: 'gmail',
            source: 'veritas_agent_dashboard'
        };

        // Send webhook request
        const response = await fetch(GMAIL_WEBHOOK_CONFIG.webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(webhookPayload)
        });

        if (!response.ok) {
            throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json().catch(() => ({}));
        console.log('Webhook triggered successfully:', result);

        return result;

    } catch (error) {
        console.error('Error triggering Gmail webhook:', error);
        throw error;
    }
}

// Store Gmail connection data in Supabase
async function storeGmailConnectionData() {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        const currentTime = new Date().toISOString();

        const gmailData = {
            user_id: user.id,
            service_name: 'gmail',
            status: 'connected',
            connected_at: currentTime,
            updated_at: currentTime,
            oauth_provider: 'webhook',
            token_scope: 'gmail integration via webhook',
            metadata: {
                email: user.email || '<EMAIL>',
                display_name: user.user_metadata?.full_name || 'User',
                profile_picture: user.user_metadata?.avatar_url || null,
                connected_via: 'webhook',
                webhook_url: GMAIL_WEBHOOK_CONFIG.webhookUrl,
                permissions: ['read', 'send'],
                last_sync: currentTime
            }
        };

        // Upsert the Gmail integration record
        const { error: upsertError } = await supabase
            .from('user_integrations')
            .upsert(gmailData, {
                onConflict: 'user_id,service_name'
            });

        if (upsertError) {
            console.error('Error storing Gmail connection data:', upsertError);
            throw upsertError;
        }

        console.log('Gmail connection data stored successfully');
    } catch (error) {
        console.error('Error in storeGmailConnectionData:', error);
        throw error;
    }
}

// Simulate integration disconnection
async function simulateIntegrationDisconnection(integration) {
    console.log(`Simulating disconnection from ${integration}...`);

    // Simulate API call delay for token revocation, cleanup, etc.
    await new Promise(resolve => setTimeout(resolve, 1500));

    // In a real implementation, this would:
    // - Revoke OAuth tokens
    // - Clear stored credentials
    // - Notify the service of disconnection
    // - Clean up any active webhooks or subscriptions

    console.log(`${integration} disconnection successful`);
    return true;
}












// Gmail OAuth2 Connection Handler
async function handleGmailOAuth2Connection() {
    console.log('Starting Gmail OAuth2 connection...');

    try {
        // Google OAuth2 configuration
        // TODO: Replace with actual Google OAuth2 client credentials
        // These should be stored securely and not hardcoded in production
        const clientId = '363533033274-4m8rdp8k6i4sn91cj8sdvu0e6oakhvgo.apps.googleusercontent.com'; // Replace with actual client ID from Google Cloud Console
        const redirectUri = 'https://veritas-agent-omega.vercel.app/';
        const scope = 'https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.send https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile';
        const responseType = 'code';
        const accessType = 'offline';
        const prompt = 'consent';

        // Build OAuth2 authorization URL
        const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
        authUrl.searchParams.append('client_id', clientId);
        authUrl.searchParams.append('redirect_uri', redirectUri);
        authUrl.searchParams.append('scope', scope);
        authUrl.searchParams.append('response_type', responseType);
        authUrl.searchParams.append('access_type', accessType);
        authUrl.searchParams.append('prompt', prompt);
        authUrl.searchParams.append('state', generateOAuthState());

        console.log('Redirecting to Google OAuth2 consent screen...');
        console.log('Callback URL:', redirectUri);

        // Redirect to Google OAuth2 consent screen
        window.location.href = authUrl.toString();

        // Note: The actual token exchange will happen in the callback handler
        // This function won't return normally since we're redirecting

    } catch (error) {
        console.error('Error initiating Gmail OAuth2 flow:', error);
        throw new Error('Failed to start Gmail authentication process');
    }
}

// Generate a secure random state parameter for OAuth2
function generateOAuthState() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

// Handle Gmail OAuth2 callback
async function handleGmailOAuth2Callback() {
    console.log('Handling Gmail OAuth2 callback...');

    try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
            throw new Error(`OAuth2 error: ${error}`);
        }

        if (!code) {
            throw new Error('No authorization code received');
        }

        console.log('Authorization code received, exchanging for tokens...');

        // Exchange authorization code for tokens
        const tokenResponse = await exchangeCodeForTokens(code);

        if (tokenResponse.access_token) {
            // Store tokens in Supabase
            await storeGmailTokens(tokenResponse);

            console.log('Gmail integration completed successfully');

            // Notify N8N backend service about successful integration
            await notifyN8NWebhook(tokenResponse);

            // Show success message
            showToast('Gmail integration connected successfully!', 'success');

            // Clean up URL parameters
            const cleanUrl = window.location.origin + window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);

            // Refresh integration status (if function exists)
            if (typeof loadUserIntegrations === 'function') {
                await loadUserIntegrations();
            } else {
                console.log('loadUserIntegrations function not available on callback page');
            }

        } else {
            throw new Error('Failed to obtain access token');
        }

    } catch (error) {
        console.error('Error handling Gmail OAuth2 callback:', error);
        showToast(`Gmail integration failed: ${error.message}`, 'error');

        // Clean up URL parameters even on error
        const cleanUrl = window.location.origin + window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);

        // Reset integration UI state
        setIntegrationConnecting('gmail', false);
    }
}

// Exchange authorization code for access tokens
async function exchangeCodeForTokens(code) {
    // TODO: Replace with actual Google OAuth2 client credentials
    // In production, client secret should be handled server-side for security
    const clientId = '363533033274-4m8rdp8k6i4sn91cj8sdvu0e6oakhvgo.apps.googleusercontent.com'; // Replace with actual client ID from Google Cloud Console
    const clientSecret = '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'; // Replace with actual client secret from Google Cloud Console
    const redirectUri = 'https://veritas-agent-omega.vercel.app/';

    const tokenEndpoint = 'https://oauth2.googleapis.com/token';

    const requestBody = new URLSearchParams({
        code: code,
        client_id: clientId,
        client_secret: clientSecret,
        redirect_uri: redirectUri,
        grant_type: 'authorization_code'
    });

    const response = await fetch(tokenEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: requestBody
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Token exchange failed: ${errorData.error_description || errorData.error}`);
    }

    return await response.json();
}

// Notify N8N webhook about successful Gmail integration
async function notifyN8NWebhook(tokenResponse) {
    try {
        console.log('Notifying N8N backend service about Gmail integration...');

        const webhookUrl = 'https://primary-production-db258.up.railway.app/webhook-test/************************************';

        // Get current user information
        const supabase = getSupabaseClient();
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError) {
            console.warn('Could not get user info for webhook:', userError);
        }

        // Prepare webhook payload with integration details
        const webhookPayload = {
            event: 'gmail_integration_completed',
            timestamp: new Date().toISOString(),
            user_id: user?.id || 'unknown',
            user_email: user?.email || 'unknown',
            integration: {
                service: 'gmail',
                status: 'connected',
                scopes: tokenResponse.scope?.split(' ') || [],
                token_type: tokenResponse.token_type || 'Bearer',
                expires_in: tokenResponse.expires_in,
                connected_at: new Date().toISOString()
            },
            // Note: We don't send actual tokens for security reasons
            // The N8N service can retrieve them from Supabase if needed
            credentials_stored: true,
            callback_url: window.location.href
        };

        console.log('Sending webhook payload:', webhookPayload);

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(webhookPayload)
        });

        console.log('Webhook response status:', response.status);
        console.log('Webhook response headers:', Object.fromEntries(response.headers.entries()));

        if (response.ok) {
            const responseText = await response.text();
            console.log('N8N webhook notification sent successfully');
            console.log('Webhook response body:', responseText);
        } else {
            const errorText = await response.text();
            console.error('N8N webhook notification failed:', {
                status: response.status,
                statusText: response.statusText,
                responseBody: errorText,
                requestUrl: webhookUrl,
                requestPayload: webhookPayload
            });
            // Don't throw error - webhook failure shouldn't break the integration
        }

    } catch (error) {
        console.error('Error sending N8N webhook notification:', error);

        // Provide specific guidance for common errors
        if (error.message.includes('CORS') || error.message.includes('Failed to fetch')) {
            console.warn('CORS or network error detected. This is likely a server-side configuration issue.');
            console.warn('To fix this, your N8N service needs to:');
            console.warn('1. Add CORS headers: Access-Control-Allow-Origin: https://veritas-agent-omega.vercel.app');
            console.warn('2. Allow OPTIONS preflight requests');
            console.warn('3. Ensure the webhook endpoint is accessible');
        }

        // Don't throw error - webhook failure shouldn't break the integration
    }
}




// Slack Connection Handler
async function handleSlackConnection() {
    console.log('Connecting to Slack...');
    // Slack OAuth2 flow
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate connection
}

// Telegram Connection Handler
async function handleTelegramConnection() {
    console.log('Connecting to Telegram...');
    // Telegram Bot API setup
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate connection
}

// Notion Connection Handler
async function handleNotionConnection() {
    console.log('Connecting to Notion...');
    // Notion OAuth2 flow
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate connection
}

// Airtable Connection Handler
async function handleAirtableConnection() {
    console.log('Connecting to Airtable...');
    // Airtable API key setup
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate connection
}

// Database Connection Handler
async function handleDatabaseConnection(dbType) {
    console.log(`Connecting to ${dbType}...`);
    // Database connection setup
    await new Promise(resolve => setTimeout(resolve, 2500)); // Simulate connection
}

// HTTP Request Connection Handler
async function handleHttpConnection() {
    console.log('Setting up HTTP Request integration...');
    // HTTP endpoint configuration
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate setup
}

// Update integration status in database
async function updateIntegrationStatus(integration, status) {
    console.log(`Updating ${integration} status to ${status}`);

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        // First try to update existing record
        const { data: updateData, error: updateError } = await supabase
            .from('user_integrations')
            .update({
                status: status,
                connected_at: status === 'connected' ? new Date().toISOString() : null,
                updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id)
            .eq('service_name', integration)
            .select();

        // If no rows were updated, insert a new record
        if (updateError || !updateData || updateData.length === 0) {
            console.log('No existing record found, inserting new one...');
            const { error: insertError } = await supabase
                .from('user_integrations')
                .insert({
                    user_id: user.id,
                    service_name: integration,
                    status: status,
                    connected_at: status === 'connected' ? new Date().toISOString() : null,
                    updated_at: new Date().toISOString()
                });

            if (insertError) {
                console.error('Error inserting integration status:', insertError);
                throw insertError;
            }
        } else if (updateError) {
            console.error('Error updating integration status:', updateError);
            throw updateError;
        }

        // Update UI
        await loadIntegrationStatuses();

    } catch (error) {
        console.error('Error in updateIntegrationStatus:', error);
        throw error;
    }
}

// Make functions available globally
window.initializeOverviewContent = initializeOverviewContent;
window.populateOverviewCompanyCards = populateOverviewCompanyCards;
window.selectOverviewCompanyCard = selectOverviewCompanyCard;
window.loadOverviewAgents = loadOverviewAgents;
window.refreshOverviewAgents = refreshOverviewAgents;
window.populateCompaniesManagement = populateCompaniesManagement;
window.toggleCompanyDetails = toggleCompanyDetails;
window.loadCompanyDetails = loadCompanyDetails;
window.loadCompanyUsers = loadCompanyUsers;
window.handleDeleteCompany = handleDeleteCompany;
window.performCompanyDeletion = performCompanyDeletion;
window.handleEditCompany = handleEditCompany;
window.setupCompanySearch = setupCompanySearch;
window.handleCompanySearch = handleCompanySearch;
window.clearCompanySearch = clearCompanySearch;
window.selectCompany = selectCompany;
window.showCompanyDetailsInPanel = showCompanyDetailsInPanel;
window.resetCompanyDetailsPanel = resetCompanyDetailsPanel;
window.buildCompanyDetailsHTML = buildCompanyDetailsHTML;
window.populateClientsManagement = populateClientsManagement;
window.loadClients = loadClients;
window.toggleClientDetails = toggleClientDetails;
window.loadClientDetails = loadClientDetails;
window.loadClientIntegrations = loadClientIntegrations;
window.handleChangeClientRole = handleChangeClientRole;
window.handleRemoveClient = handleRemoveClient;
window.setupClientSearch = setupClientSearch;
window.handleClientSearch = handleClientSearch;
window.clearClientSearch = clearClientSearch;
window.selectClient = selectClient;
window.showClientDetailsInPanel = showClientDetailsInPanel;
window.resetClientDetailsPanel = resetClientDetailsPanel;
window.initializeIntegrationsPage = initializeIntegrationsPage;
window.loadIntegrationStatuses = loadIntegrationStatuses;
window.handleIntegrationDisconnect = handleIntegrationDisconnect;
window.setIntegrationDisconnecting = setIntegrationDisconnecting;
window.simulateIntegrationDisconnection = simulateIntegrationDisconnection;







    // Pre-fill user email if available
    const userEmail = window.cachedUserProfile?.email || currentUser?.email;
    if (userEmail) {
        const emailField = document.getElementById('support-email');
        if (emailField) {
            emailField.value = userEmail;
        }
    }


// Help & Support Functions

// Initialize Help & Support page based on user role
async function initializeHelpSupportPage() {
    console.log('initializeHelpSupportPage called');

    // Get user role
    const userRole = window.cachedUserProfile?.role || currentUser?.role || 'Client';
    console.log('User role for Help & Support:', userRole);

    if (userRole === 'Client') {
        // For Client users, hide all FAQ sections except "Need More Help"
        hideClientFAQSections();
    }
    // For Owner users, show full Help & Support page (no modifications needed)
}

// Hide FAQ sections for Client role users
function hideClientFAQSections() {
    console.log('Hiding FAQ sections for Client role user');

    // Wait a moment for the DOM to be ready
    setTimeout(() => {
        const helpSupportView = document.getElementById('dashboard-help-support-view');
        if (helpSupportView) {
            console.log('Found help support view, hiding sections...');

            // Get all child divs of the help support view
            const childDivs = helpSupportView.children;

            // Hide everything except the title (first div) and the contact form (last div)
            for (let i = 0; i < childDivs.length; i++) {
                const div = childDivs[i];

                // Keep the title div (first one with h2)
                if (div.querySelector('h2')) {
                    console.log('Keeping title section');
                    continue;
                }

                // Keep the contact support form (has form#support-form)
                if (div.querySelector('#support-form')) {
                    console.log('Keeping contact form section');
                    continue;
                }

                // Hide everything else (Quick Help Cards and FAQ sections)
                div.style.display = 'none';
                console.log('Hidden section:', div.className || 'unnamed div');
            }
        }

        // Update the page title and description for Client users
        const helpTitle = document.querySelector('#dashboard-help-support-view h2');
        const helpDescription = document.querySelector('#dashboard-help-support-view p');

        if (helpTitle) {
            helpTitle.textContent = 'Contact Support';
        }

        if (helpDescription) {
            helpDescription.textContent = 'Get help from our support team';
        }

        console.log('Help & Support page customized for Client role');
    }, 100);
}














// Function to load integration status for help page
async function loadIntegrationStatusForHelp() {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) return;

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) return;

        // Check Gmail integration status only
        const { data: integrations, error } = await supabase
            .from('user_integrations')
            .select('service_name, status')
            .eq('user_id', user.id)
            .in('service_name', ['gmail']);

        // Create status map
        const statusMap = {};
        if (!error && integrations) {
            integrations.forEach(integration => {
                statusMap[integration.service_name] = integration.status;
            });
        }

        // Update Gmail status
        const gmailStatusElement = document.getElementById('gmail-status');
        const gmailIconElement = gmailStatusElement?.parentElement.querySelector('i');
        if (gmailStatusElement) {
            const isConnected = statusMap['gmail'] === 'connected';
            gmailStatusElement.textContent = isConnected ? 'Connected' : 'Not Connected';
            gmailStatusElement.className = isConnected ? 'text-green-400 text-sm font-medium' : 'text-red-400 text-sm font-medium';
            if (gmailIconElement) {
                gmailIconElement.className = isConnected ? 'fas fa-envelope text-green-400 text-sm' : 'fas fa-envelope text-red-400 text-sm';
            }
        }



        // Update support form based on Gmail status
        const hasGmail = statusMap['gmail'] === 'connected';
        updateSupportFormForEmailIntegration(hasGmail);

    } catch (error) {
        console.log('Could not load integration status for help:', error);
        // Default to not connected
        updateSupportFormForEmailIntegration(false);
    }
}

// Function to update support form UI based on email integration status
function updateSupportFormForEmailIntegration(hasEmailIntegration) {
    const form = document.getElementById('support-form');
    const statusIndicator = document.getElementById('email-integration-status');
    const emailPrompt = document.getElementById('email-integration-prompt');

    if (!form) return;

    // Show/hide email integration status indicator
    if (statusIndicator) {
        if (hasEmailIntegration) {
            statusIndicator.classList.remove('hidden');
        } else {
            statusIndicator.classList.add('hidden');
        }
    }

    // Show/hide email integration prompt
    if (emailPrompt) {
        if (hasEmailIntegration) {
            emailPrompt.classList.add('hidden');
        } else {
            emailPrompt.classList.remove('hidden');
        }
    }

    // Find the response time message
    const responseMessage = form.querySelector('p');
    if (responseMessage && (responseMessage.textContent.includes('24 hours') || responseMessage.textContent.includes('Gmail account'))) {
        if (hasEmailIntegration) {
            responseMessage.innerHTML = `
                <i class="fas fa-envelope mr-1 text-green-400"></i>
                Your support request will be sent via your connected Gmail account
            `;
            responseMessage.className = 'text-sm text-green-400';
        } else {
            responseMessage.innerHTML = `
                <i class="fas fa-clock mr-1"></i>
                We typically respond within 24 hours
            `;
            responseMessage.className = 'text-sm text-gray-400';
        }
    }
}

// Function to handle dashboard support form submission (different from main support form)
async function handleDashboardSupportFormSubmission(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const subject = formData.get('subject');
    const message = formData.get('message');

    // Dashboard support form doesn't have priority field, default to medium
    const priority = 'medium';

    // Get user information
    const userEmail = window.cachedUserProfile?.email || currentUser?.email;
    const userName = window.cachedUserProfile?.fullName || currentUser?.fullName;
    const userCompany = window.cachedUserProfile?.company || currentUser?.company;

    // Clear any existing errors
    clearFormErrors(form);

    // Validate form
    const errors = validateForm({ subject, message }, {
        subject: { required: true, label: 'Subject' },
        message: { required: true, label: 'Message' }
    });

    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }

    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);

    try {
        // Check if user has email integration connected
        const hasEmailIntegration = await checkUserEmailIntegration();

        // Prepare support ticket data
        const supportData = {
            subject: subject.trim(),
            priority: priority,
            message: message.trim(),
            user_email: userEmail,
            user_name: userName,
            user_company: userCompany,
            timestamp: new Date().toISOString(),
            source: 'dashboard-support-view',
            via_email_integration: hasEmailIntegration
        };

        console.log('Submitting dashboard support request:', supportData);

        let success = false;
        let method = '';

        if (hasEmailIntegration) {
            // Try to send via email integration first
            try {
                await sendSupportTicketViaEmail(supportData);
                success = true;
                method = 'email integration';
            } catch (emailError) {
                console.warn('Email integration failed, falling back to webhook:', emailError);
                // Fall back to webhook
                await sendSupportTicketViaWebhook(supportData);
                success = true;
                method = 'webhook (email fallback)';
            }
        } else {
            // Send via webhook
            await sendSupportTicketViaWebhook(supportData);
            success = true;
            method = 'webhook';
        }

        if (success) {
            const successMessage = hasEmailIntegration && method === 'email integration'
                ? 'Support request sent via your Gmail account! We\'ll respond to your email directly.'
                : 'Support request submitted successfully! We\'ll get back to you within 24 hours.';

            showToast(successMessage, 'success');
            form.reset();

            // Show success message in the status div
            const statusDiv = document.getElementById('dashboard-support-status');
            if (statusDiv) {
                statusDiv.innerHTML = `<div class="text-green-400"><i class="fas fa-check-circle mr-2"></i>${successMessage}</div>`;
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 5000);
            }
        }

    } catch (error) {
        console.error('Dashboard support form submission error:', error);
        showFormError(form, 'Failed to submit support request. Please try again or contact us directly.');

        // Show error message in the status div
        const statusDiv = document.getElementById('dashboard-support-status');
        if (statusDiv) {
            statusDiv.innerHTML = `<div class="text-red-400"><i class="fas fa-exclamation-circle mr-2"></i>Failed to submit support request. Please try again.</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }
    } finally {
        setLoadingState(submitButton, false);
    }
}

// Function to handle support form submission (main support page)
async function handleSupportFormSubmission(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const subject = formData.get('subject');
    const priority = formData.get('priority');
    const message = formData.get('message');

    // Get user information
    const userEmail = window.cachedUserProfile?.email || currentUser?.email;
    const userName = window.cachedUserProfile?.fullName || currentUser?.fullName;
    const userCompany = window.cachedUserProfile?.company || currentUser?.company;

    // Clear any existing errors
    clearFormErrors(form);

    // Validate form
    const errors = validateForm({ subject, message }, {
        subject: { required: true, label: 'Subject' },
        message: { required: true, label: 'Message' }
    });

    if (errors.length > 0) {
        showFormError(form, errors[0]);
        return;
    }

    const submitButton = form.querySelector('button[type="submit"]');
    setLoadingState(submitButton, true);

    try {
        // Check if user has email integration connected
        const hasEmailIntegration = await checkUserEmailIntegration();

        // Prepare support ticket data
        const supportData = {
            subject: subject.trim(),
            priority: priority,
            message: message.trim(),
            user_email: userEmail,
            user_name: userName,
            user_company: userCompany,
            timestamp: new Date().toISOString(),
            source: 'dashboard-help-support',
            via_email_integration: hasEmailIntegration
        };

        console.log('Submitting support request:', supportData);

        let success = false;
        let method = '';

        if (hasEmailIntegration) {
            // Try to send via email integration first
            try {
                await sendSupportTicketViaEmail(supportData);
                success = true;
                method = 'email integration';
            } catch (emailError) {
                console.warn('Email integration failed, falling back to webhook:', emailError);
                // Fall back to webhook
                await sendSupportTicketViaWebhook(supportData);
                success = true;
                method = 'webhook (email fallback)';
            }
        } else {
            // Send via webhook
            await sendSupportTicketViaWebhook(supportData);
            success = true;
            method = 'webhook';
        }

        if (success) {
            const successMessage = hasEmailIntegration && method === 'email integration'
                ? 'Support request sent via your Gmail account! We\'ll respond to your email directly.'
                : 'Support request submitted successfully! We\'ll get back to you within 24 hours.';

            showToast(successMessage, 'success');
            form.reset();

            // Reset priority to default
            const prioritySelect = form.querySelector('#support-priority');
            if (prioritySelect) {
                prioritySelect.value = 'medium';
            }
        }

    } catch (error) {
        console.error('Support form submission error:', error);
        showFormError(form, 'Failed to submit support request. Please try again or contact us directly.');
    } finally {
        setLoadingState(submitButton, false);
    }
}


// Function to check if user has email integration connected
async function checkUserEmailIntegration() {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) return false;

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) return false;

        // Check if user has Gmail integration connected
        const { data: integrations, error } = await supabase
            .from('user_integrations')
            .select('service_name, status')
            .eq('user_id', user.id)
            .eq('service_name', 'gmail')
            .eq('status', 'connected')
            .single();

        return !error && integrations;
    } catch (error) {
        console.log('Could not check email integration:', error);
        return false;
    }
}

// Function to send support ticket via email integration
async function sendSupportTicketViaEmail(supportData) {
    console.log('Sending support ticket via email integration...');

    // Support email address (this should be configurable)
    const supportEmail = '';

    // Create email content
    const emailSubject = `[Support] ${supportData.subject} (Priority: ${supportData.priority.toUpperCase()})`;
    const emailBody = `
Support Request Details:
========================

From: ${supportData.user_name} (${supportData.user_email})
Company: ${supportData.user_company}
Priority: ${supportData.priority.toUpperCase()}
Submitted: ${new Date(supportData.timestamp).toLocaleString()}
Source: Dashboard Support

Message:
--------
${supportData.message}

========================
This ticket was sent via the user's connected Gmail integration.
Please respond directly to this email.
    `.trim();

    // In a real implementation, this would use the Gmail API with the user's OAuth token
    // For now, we'll simulate the email sending process

    // Simulate API call to Gmail
    const emailPayload = {
        to: supportEmail,
        subject: emailSubject,
        body: emailBody,
        from: supportData.user_email
    };

    console.log('Email payload:', emailPayload);

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // In production, you would:
    // 1. Get the user's Gmail OAuth token from the database
    // 2. Use the Gmail API to send the email
    // 3. Handle any API errors appropriately

    console.log('Support ticket sent via email integration successfully');
}

// Function to send support ticket via webhook (fallback method)
async function sendSupportTicketViaWebhook(supportData) {
    console.log('Sending support ticket via webhook...');

    // Use the dedicated support API endpoint
    const webhookUrl = '/api/support';

    // Send the support data directly (no need to reformat)
    const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(supportData)
    });

    if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Support API request failed with status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Support ticket sent via webhook successfully:', result);

    return result;
}



// Notifications Page Functions

// Pre-load notifications data without updating UI (for faster loading when user visits notifications page)
async function preloadNotificationsData() {
    console.log('Pre-loading notifications data...');

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            console.log('No Supabase client available for notifications preload');
            return;
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            console.log('No valid user session for notifications preload');
            return;
        }

        // Get user role to determine filtering
        const userRole = currentUser?.role || 'Client';

        // Build query for notifications (same logic as loadNotifications but without UI update)
        let query = supabase
            .from('activity_logs')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(20);

        // For Client users, exclude login and welcome back notifications
        if (userRole === 'Client') {
            query = query.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
        }

        const { data: notifications, error } = await query;

        if (error) {
            console.log('Error pre-loading notifications:', error);
            return;
        }

        // Cache the notifications data for faster loading later
        window.cachedNotifications = notifications || [];
        console.log('Notifications data pre-loaded successfully:', notifications?.length || 0, 'items');

    } catch (error) {
        console.log('Error in preloadNotificationsData:', error);
    }
}

// Initialize notifications page
async function initializeNotificationsPage() {
    console.log('initializeNotificationsPage called');

    // Ensure we have a valid user session before loading notifications
    const supabase = getSupabaseClient();
    if (!supabase) {
        console.error('No Supabase client available for notifications');
        return;
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
        console.error('No valid user session for notifications');
        return;
    }

    console.log('Loading notifications for user:', user.id);

    // Small delay to ensure user session is fully established
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if we have cached notifications data from preload
    if (window.cachedNotifications && window.cachedNotifications.length > 0) {
        console.log('Using cached notifications data for faster loading');
        updateNotificationsUI(window.cachedNotifications, true);
        // Clear the cache after using it
        window.cachedNotifications = null;
    } else {
        // Load user's activity logs and notifications if no cache available
        await loadNotifications();
    }

    await loadNotificationStatistics();

    // Setup event listeners for notification controls
    setupNotificationEventListeners();
}

// Load notifications from database
async function loadNotifications(filter = 'all', offset = 0, limit = 20) {
    console.log('Loading notifications...', { filter, offset, limit });

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        // Get user role to determine filtering
        const userRole = currentUser?.role || 'Client';

        // Query activity logs for the current user
        let query = supabase
            .from('activity_logs')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .range(offset, offset + limit - 1);

        // Apply filters
        if (filter === 'unread') {
            query = query.eq('is_read', false);
        } else if (filter !== 'all') {
            query = query.eq('category', filter);
        }

        // For Client users, exclude login and welcome back notifications
        if (userRole === 'Client') {
            query = query.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
        }

        const { data: notifications, error } = await query;

        if (error) {
            console.error('Error loading notifications:', error);
            throw error;
        }

        console.log('Notifications loaded:', notifications);

        // Update UI with notifications
        updateNotificationsUI(notifications || [], offset === 0);

        // Update notification badge
        await updateNotificationBadge();

        return notifications || [];

    } catch (error) {
        console.error('Error in loadNotifications:', error);
        showNotificationsError('Failed to load notifications');
        return [];
    }
}

// Load notification statistics
async function loadNotificationStatistics() {
    console.log('Loading notification statistics...');

    try {
        const supabase = getSupabaseClient();
        if (!supabase) {
            throw new Error('Database connection not available');
        }

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) {
            throw new Error('User authentication required');
        }

        // Get user role to determine filtering
        const userRole = currentUser?.role || 'Client';

        const now = new Date();
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        // Build base queries
        let todayQuery = supabase
            .from('activity_logs')
            .select('id', { count: 'exact' })
            .eq('user_id', user.id)
            .gte('created_at', todayStart.toISOString());

        let unreadQuery = supabase
            .from('activity_logs')
            .select('id', { count: 'exact' })
            .eq('user_id', user.id)
            .eq('is_read', false);

        let weekQuery = supabase
            .from('activity_logs')
            .select('id', { count: 'exact' })
            .eq('user_id', user.id)
            .gte('created_at', weekStart.toISOString());

        // For Client users, exclude login and welcome back notifications
        if (userRole === 'Client') {
            todayQuery = todayQuery.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
            unreadQuery = unreadQuery.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
            weekQuery = weekQuery.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
        }

        // Execute queries
        const { data: todayData, error: todayError } = await todayQuery;
        const { data: unreadData, error: unreadError } = await unreadQuery;
        const { data: weekData, error: weekError } = await weekQuery;

        if (todayError || unreadError || weekError) {
            throw new Error('Failed to load statistics');
        }

        // Update statistics UI
        document.getElementById('today-activity-count').textContent = todayData?.length || 0;
        document.getElementById('unread-count').textContent = unreadData?.length || 0;
        document.getElementById('week-activity-count').textContent = weekData?.length || 0;

    } catch (error) {
        console.error('Error loading notification statistics:', error);
        // Set default values on error
        document.getElementById('today-activity-count').textContent = '-';
        document.getElementById('unread-count').textContent = '-';
        document.getElementById('week-activity-count').textContent = '-';
    }
}

// Update notifications UI
function updateNotificationsUI(notifications, clearExisting = true) {
    const container = document.getElementById('notifications-container');
    const loadingElement = document.getElementById('notifications-loading');
    const loadMoreContainer = document.getElementById('load-more-container');

    if (!container) return;

    // Hide loading
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }

    if (clearExisting) {
        container.innerHTML = '';
    }

    if (notifications.length === 0 && clearExisting) {
        container.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-bell-slash text-4xl text-gray-500 mb-4"></i>
                <h4 class="text-lg font-medium text-gray-300 mb-2">No Notifications</h4>
                <p class="text-sm text-gray-400">You're all caught up! New activity will appear here.</p>
            </div>
        `;
        return;
    }

    notifications.forEach(notification => {
        const notificationElement = createNotificationElement(notification);
        container.appendChild(notificationElement);
    });

    // Show/hide load more button
    if (loadMoreContainer) {
        loadMoreContainer.style.display = notifications.length >= 20 ? 'block' : 'none';
    }
}

// Create notification element
function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification-item ${notification.is_read ? 'read' : 'unread'}`;
    div.setAttribute('data-notification-id', notification.id);

    const timeAgo = getTimeAgo(new Date(notification.created_at));
    const icon = getNotificationIcon(notification.category, notification.type);

    div.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="notification-icon ${notification.category}">
                <i class="fas ${icon}"></i>
            </div>
            <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-white mb-1">${notification.title}</h4>
                        <p class="text-sm text-gray-400 mb-2">${notification.message}</p>
                        <div class="flex items-center space-x-3 text-xs">
                            <span class="notification-time">${timeAgo}</span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(notification.category)}">${notification.category}</span>
                        </div>
                    </div>
                    ${!notification.is_read ? '<div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-1"></div>' : ''}
                </div>
            </div>
        </div>
    `;

    // Add click handler to mark as read
    div.addEventListener('click', () => markNotificationAsRead(notification.id));

    return div;
}

// Get notification icon based on category and type
function getNotificationIcon(category, type) {
    const icons = {
        system: {
            info: 'fa-info-circle',
            success: 'fa-check-circle',
            warning: 'fa-exclamation-triangle',
            error: 'fa-times-circle'
        },
        integration: {
            connected: 'fa-plug',
            disconnected: 'fa-unlink',
            error: 'fa-exclamation-triangle'
        },
        agents: {
            created: 'fa-robot',
            updated: 'fa-cog',
            deleted: 'fa-trash'
        },
        user: {
            login: 'fa-sign-in-alt',
            profile_updated: 'fa-user-edit',
            settings_changed: 'fa-cog'
        }
    };

    return icons[category]?.[type] || 'fa-bell';
}

// Get category color classes
function getCategoryColor(category) {
    const colors = {
        system: 'bg-blue-500/20 text-blue-400',
        integration: 'bg-green-500/20 text-green-400',
        agents: 'bg-purple-500/20 text-purple-400',
        user: 'bg-cyan-500/20 text-cyan-400'
    };
    return colors[category] || 'bg-gray-500/20 text-gray-400';
}

// Setup event listeners for notifications
function setupNotificationEventListeners() {
    console.log('Setting up notification event listeners...');

    // Filter tabs
    const filterTabs = document.querySelectorAll('.notification-filter-tab');
    filterTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            e.preventDefault();
            const filter = tab.getAttribute('data-filter');
            setActiveFilterTab(tab);
            loadNotifications(filter);
        });
    });

    // Mark all as read button
    const markAllReadBtn = document.getElementById('mark-all-read-btn');
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', markAllNotificationsAsRead);
    }

    // Clear all notifications button
    const clearAllBtn = document.getElementById('clear-all-notifications-btn');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', clearAllNotifications);
    }

    // Refresh button
    const refreshBtn = document.getElementById('refresh-notifications-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            const activeFilter = document.querySelector('.notification-filter-tab.active')?.getAttribute('data-filter') || 'all';
            loadNotifications(activeFilter);
        });
    }

    // Load more button
    const loadMoreBtn = document.getElementById('load-more-notifications-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreNotifications);
    }
}

// Set active filter tab
function setActiveFilterTab(activeTab) {
    const filterTabs = document.querySelectorAll('.notification-filter-tab');
    filterTabs.forEach(tab => {
        tab.classList.remove('active');
    });
    activeTab.classList.add('active');
}

// Mark notification as read
async function markNotificationAsRead(notificationId) {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) return;

        const { error } = await supabase
            .from('activity_logs')
            .update({ is_read: true, updated_at: new Date().toISOString() })
            .eq('id', notificationId);

        if (error) {
            console.error('Error marking notification as read:', error);
            return;
        }

        // Update UI
        const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
        if (notificationElement) {
            notificationElement.classList.remove('unread');
            notificationElement.classList.add('read');

            // Remove unread indicator
            const unreadIndicator = notificationElement.querySelector('.w-2.h-2.bg-blue-500');
            if (unreadIndicator) {
                unreadIndicator.remove();
            }
        }

        // Update badge and statistics
        await updateNotificationBadge();
        await loadNotificationStatistics();

    } catch (error) {
        console.error('Error in markNotificationAsRead:', error);
    }
}

// Mark all notifications as read
async function markAllNotificationsAsRead() {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) return;

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) return;

        // Get user role to determine filtering
        const userRole = currentUser?.role || 'Client';

        // Build query to mark notifications as read
        let query = supabase
            .from('activity_logs')
            .update({ is_read: true, updated_at: new Date().toISOString() })
            .eq('user_id', user.id)
            .eq('is_read', false);

        // For Client users, exclude login and welcome back notifications
        if (userRole === 'Client') {
            query = query.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
        }

        const { error } = await query;

        if (error) {
            console.error('Error marking all notifications as read:', error);
            showToast('Failed to mark notifications as read', 'error');
            return;
        }

        showToast('All notifications marked as read', 'success');

        // Refresh the current view
        const activeFilter = document.querySelector('.notification-filter-tab.active')?.getAttribute('data-filter') || 'all';
        await loadNotifications(activeFilter);
        await updateNotificationBadge();
        await loadNotificationStatistics();

    } catch (error) {
        console.error('Error in markAllNotificationsAsRead:', error);
        showToast('Failed to mark notifications as read', 'error');
    }
}

// Clear all notifications
async function clearAllNotifications() {
    // Get user role to determine what notifications can be cleared
    const userRole = currentUser?.role || 'Client';

    // Show confirmation dialog with appropriate message
    let confirmMessage = 'Are you sure you want to clear all notifications?\n\nThis action cannot be undone and will permanently delete all your activity logs.';
    if (userRole === 'Client') {
        confirmMessage = 'Are you sure you want to clear all notifications?\n\nThis action cannot be undone and will permanently delete your activity logs (excluding login and welcome messages).';
    }

    const confirmed = confirm(confirmMessage);

    if (!confirmed) {
        return;
    }

    try {
        const supabase = getSupabaseClient();
        if (!supabase) return;

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) return;

        // Build query to delete notifications
        let query = supabase
            .from('activity_logs')
            .delete()
            .eq('user_id', user.id);

        // For Client users, exclude login and welcome back notifications
        if (userRole === 'Client') {
            query = query.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
        }

        const { error } = await query;

        if (error) {
            console.error('Error clearing all notifications:', error);
            showToast('Failed to clear notifications', 'error');
            return;
        }

        showToast('All notifications cleared successfully', 'success');

        // Refresh the current view
        const activeFilter = document.querySelector('.notification-filter-tab.active')?.getAttribute('data-filter') || 'all';
        await loadNotifications(activeFilter);
        await updateNotificationBadge();
        await loadNotificationStatistics();

    } catch (error) {
        console.error('Error in clearAllNotifications:', error);
        showToast('Failed to clear notifications', 'error');
    }
}

// Update notification badge
async function updateNotificationBadge() {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) return;

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) return;

        // Get user role to determine filtering
        const userRole = currentUser?.role || 'Client';

        // Build query for unread notifications
        let query = supabase
            .from('activity_logs')
            .select('id', { count: 'exact' })
            .eq('user_id', user.id)
            .eq('is_read', false);

        // For Client users, exclude login and welcome back notifications
        if (userRole === 'Client') {
            query = query.not('type', 'eq', 'login').not('type', 'eq', 'welcome');
        }

        const { data, error } = await query;

        if (error) {
            console.error('Error getting unread count:', error);
            return;
        }

        const unreadCount = data?.length || 0;
        const badge = document.getElementById('notification-badge');

        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount.toString();
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }

    } catch (error) {
        console.error('Error in updateNotificationBadge:', error);
    }
}

// Utility functions for notifications
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        return date.toLocaleDateString();
    }
}

function showNotificationsError(message) {
    const container = document.getElementById('notifications-container');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-8 bg-red-900/20 rounded-lg border border-red-700">
                <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                <p class="text-red-300 mb-2">${message}</p>
                <button onclick="loadNotifications()" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors">
                    <i class="fas fa-retry mr-1"></i>Retry
                </button>
            </div>
        `;
    }
}

// Activity logging functions
async function logActivity(category, type, title, message, metadata = {}) {
    try {
        const supabase = getSupabaseClient();
        if (!supabase) return;

        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError || !user) return;

        const { error } = await supabase
            .from('activity_logs')
            .insert([{
                user_id: user.id,
                category: category,
                type: type,
                title: title,
                message: message,
                metadata: metadata,
                is_read: false,
                created_at: new Date().toISOString()
            }]);

        if (error) {
            console.error('Error logging activity:', error);
            return;
        }

        // Update notification badge if on notifications page
        if (window.location.hash.includes('notifications') || document.getElementById('dashboard-notifications-view')?.classList.contains('active')) {
            await updateNotificationBadge();
        }

    } catch (error) {
        console.error('Error in logActivity:', error);
    }
}

// Specific activity logging functions
async function logUserActivity(type, title, message, metadata = {}) {
    await logActivity('user', type, title, message, metadata);
}

async function logSystemActivity(type, title, message, metadata = {}) {
    await logActivity('system', type, title, message, metadata);
}

async function logIntegrationActivity(type, title, message, metadata = {}) {
    await logActivity('integration', type, title, message, metadata);
}

async function logAgentActivity(type, title, message, metadata = {}) {
    await logActivity('agents', type, title, message, metadata);
}

// Load more notifications
let notificationOffset = 0;
async function loadMoreNotifications() {
    notificationOffset += 20;
    const activeFilter = document.querySelector('.notification-filter-tab.active')?.getAttribute('data-filter') || 'all';
    await loadNotifications(activeFilter, notificationOffset, 20);
}

// Function to refresh notifications if the notifications page is currently visible
async function refreshNotificationsIfVisible() {
    try {
        const notificationsView = document.getElementById('dashboard-notifications-view');
        if (notificationsView && !notificationsView.classList.contains('hidden')) {
            console.log('Refreshing notifications page after integration change...');
            // Add a small delay to ensure the database trigger has completed
            setTimeout(async () => {
                await loadNotifications();
                await loadNotificationStatistics();
                await updateNotificationBadge();
            }, 500);
        } else {
            // Just update the badge if not on notifications page
            await updateNotificationBadge();
        }
    } catch (error) {
        console.error('Error refreshing notifications:', error);
    }
}

// Function to create sample notifications for testing
async function createSampleNotifications() {
    console.log('Creating sample notifications for testing...');

    try {
        // Create various types of sample notifications
        await logSystemActivity('info', 'Welcome to Veritas Agent', 'Thank you for joining Veritas Agent! Explore the dashboard to get started.', {});

        await logSystemActivity('success', 'System Update', 'Your system has been updated with the latest features and security improvements.', {});

        await logIntegrationActivity('info', 'Integration Available', 'Gmail integration is now available. Connect your email to enhance your AI agent capabilities.', { service_name: 'gmail' });

        await logUserActivity('profile_updated', 'Profile Setup Complete', 'Your profile has been successfully set up. You can update your information anytime in Settings.', {});

        await logSystemActivity('info', 'Getting Started Tips', 'Explore the dashboard to discover all the features available for your AI agents.', {});

        showToast('Sample notifications created successfully!', 'success');

        // Refresh notifications if on notifications page
        await refreshNotificationsIfVisible();

    } catch (error) {
        console.error('Error creating sample notifications:', error);
        showToast('Failed to create sample notifications', 'error');
    }
}

// Make functions globally available
window.activateDashboardView = activateDashboardView;
window.showDashboardView = activateDashboardView; // Alias for compatibility
window.logUserActivity = logUserActivity;
window.logSystemActivity = logSystemActivity;
window.logIntegrationActivity = logIntegrationActivity;
window.logAgentActivity = logAgentActivity;
window.createSampleNotifications = createSampleNotifications;
window.initializeHelpSupportPage = initializeHelpSupportPage;
window.hideClientFAQSections = hideClientFAQSections;

// Make form handlers available globally for main.js
window.handleContactForm = handleContactForm;
window.handleAIAgentForm = handleAIAgentForm;
